package model

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries    int           // 最大重试次数
	InitialDelay  time.Duration // 初始延迟
	MaxDelay      time.Duration // 最大延迟
	BackoffFactor float64       // 退避因子
}

// DefaultRetryConfig 默认重试配置
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
	}
}

// BatchMetrics 批量写入指标
type BatchMetrics struct {
	TotalBatches    int64         // 总批次数
	TotalLogs       int64         // 总日志数
	FailedBatches   int64         // 失败批次数
	FailedLogs      int64         // 失败日志数
	LastFlushTime   time.Time     // 最后刷新时间
	AvgBatchSize    float64       // 平均批次大小
	AvgFlushTime    time.Duration // 平均刷新时间
	BackpressureHit int64         // 背压触发次数
	mu              sync.RWMutex  // 保护指标的读写锁
}

// UpdateBatchMetrics 更新批量写入指标
func (m *BatchMetrics) UpdateBatchMetrics(batchSize int, flushTime time.Duration, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.TotalBatches++
	m.TotalLogs += int64(batchSize)
	m.LastFlushTime = time.Now()

	if !success {
		m.FailedBatches++
		m.FailedLogs += int64(batchSize)
	}

	// 计算平均值
	m.AvgBatchSize = float64(m.TotalLogs) / float64(m.TotalBatches)

	// 简单的移动平均
	if m.AvgFlushTime == 0 {
		m.AvgFlushTime = flushTime
	} else {
		m.AvgFlushTime = (m.AvgFlushTime + flushTime) / 2
	}
}

// IncrementBackpressure 增加背压计数
func (m *BatchMetrics) IncrementBackpressure() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.BackpressureHit++
}

// GetMetrics 获取指标快照
func (m *BatchMetrics) GetMetrics() BatchMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return BatchMetrics{
		TotalBatches:    m.TotalBatches,
		TotalLogs:       m.TotalLogs,
		FailedBatches:   m.FailedBatches,
		FailedLogs:      m.FailedLogs,
		LastFlushTime:   m.LastFlushTime,
		AvgBatchSize:    m.AvgBatchSize,
		AvgFlushTime:    m.AvgFlushTime,
		BackpressureHit: m.BackpressureHit,
		// 不复制锁
	}
}

// StorageMetrics Elasticsearch存储监控指标
type StorageMetrics struct {
	// 连接指标
	TotalRequests   int64         `json:"total_requests"`
	FailedRequests  int64         `json:"failed_requests"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	LastRequestTime time.Time     `json:"last_request_time"`

	// 健康状态
	HealthyHosts    int       `json:"healthy_hosts"`
	TotalHosts      int       `json:"total_hosts"`
	LastHealthCheck time.Time `json:"last_health_check"`

	// 索引指标
	TotalIndices   int   `json:"total_indices"`
	TotalDocuments int64 `json:"total_documents"`
	IndexSize      int64 `json:"index_size_bytes"`

	// 查询指标
	TotalQueries int64 `json:"total_queries"`

	// 批量写入指标
	BatchMetrics BatchMetrics `json:"batch_metrics"`

	mu sync.RWMutex `json:"-"`
}

// UpdateRequestMetrics 更新请求指标
func (s *StorageMetrics) UpdateRequestMetrics(responseTime time.Duration, success bool) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.TotalRequests++
	s.LastRequestTime = time.Now()

	if !success {
		s.FailedRequests++
	}

	// 简单的移动平均
	if s.AvgResponseTime == 0 {
		s.AvgResponseTime = responseTime
	} else {
		s.AvgResponseTime = (s.AvgResponseTime + responseTime) / 2
	}
}

// UpdateHealthMetrics 更新健康指标
func (s *StorageMetrics) UpdateHealthMetrics(healthyCount, totalCount int) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.HealthyHosts = healthyCount
	s.TotalHosts = totalCount
	s.LastHealthCheck = time.Now()
}

// UpdateQueryMetrics 更新查询指标
func (s *StorageMetrics) UpdateQueryMetrics() {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.TotalQueries++
}

// GetStorageMetrics 获取存储指标快照
func (s *StorageMetrics) GetStorageMetrics() StorageMetrics {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return StorageMetrics{
		TotalRequests:   s.TotalRequests,
		FailedRequests:  s.FailedRequests,
		AvgResponseTime: s.AvgResponseTime,
		LastRequestTime: s.LastRequestTime,
		HealthyHosts:    s.HealthyHosts,
		TotalHosts:      s.TotalHosts,
		LastHealthCheck: s.LastHealthCheck,
		TotalIndices:    s.TotalIndices,
		TotalDocuments:  s.TotalDocuments,
		IndexSize:       s.IndexSize,
		TotalQueries:    s.TotalQueries,
		BatchMetrics:    s.BatchMetrics.GetMetrics(),
	}
}

// parseConnectionString 解析连接字符串
// 支持格式：
// 1. *****************************1:port1,host2:port2
// 2. host1:port1,host2:port2
// 3. http://host1:port1,http://host2:port2
func parseConnectionString(connectionString string) ([]string, string, string) {
	var username, password string
	var hosts []string

	// 分割多个主机
	hostParts := strings.Split(connectionString, ",")

	for i, hostPart := range hostParts {
		hostPart = strings.TrimSpace(hostPart)

		// 只从第一个主机中提取用户名和密码
		if i == 0 && strings.Contains(hostPart, "@") {
			// 解析用户名和密码
			if strings.HasPrefix(hostPart, "http://") || strings.HasPrefix(hostPart, "https://") {
				// 格式: *****************************:port
				parts := strings.SplitN(hostPart, "://", 2)
				if len(parts) == 2 {
					protocol := parts[0] + "://"
					remaining := parts[1]

					if strings.Contains(remaining, "@") {
						authAndHost := strings.SplitN(remaining, "@", 2)
						if len(authAndHost) == 2 {
							auth := authAndHost[0]
							host := authAndHost[1]

							if strings.Contains(auth, ":") {
								authParts := strings.SplitN(auth, ":", 2)
								username = authParts[0]
								password = authParts[1]
							} else {
								username = auth
							}

							hostPart = protocol + host
						}
					}
				}
			}
		}

		hosts = append(hosts, hostPart)
	}

	return hosts, username, password
}

// ElasticsearchLogStorage Elasticsearch日志存储实现
type ElasticsearchLogStorage struct {
	client       *http.Client
	hosts        []string
	username     string
	password     string
	indexName    string
	batchLogs    []*Log
	batchMu      sync.Mutex
	stopCh       chan struct{}
	wg           sync.WaitGroup
	healthStatus map[string]bool // 记录每个主机的健康状态
	healthMu     sync.RWMutex    // 保护健康状态的读写锁
	retryConfig  RetryConfig     // 重试配置

	// 批量写入优化
	batchCh      chan *Log     // 批量写入通道
	flushTicker  *time.Ticker  // 刷新定时器
	backpressure chan struct{} // 背压控制
	metrics      BatchMetrics  // 批量写入指标

	// 查询优化已移除缓存，确保数据实时性

	// 监控指标
	storageMetrics StorageMetrics // 存储监控指标

	// ID生成器
	logIDGenerator       *IDGenerator // Log表ID生成器
	logExtendIDGenerator *IDGenerator // LogExtend表ID生成器
}

// ElasticsearchLog Elasticsearch中的日志文档结构
type ElasticsearchLog struct {
	ID                        string    `json:"id"`
	RequestID                 string    `json:"request_id"`
	UserID                    int       `json:"user_id"`
	CreatedAt                 time.Time `json:"created_at"`
	Type                      int       `json:"type"`
	Content                   string    `json:"content"`
	Username                  string    `json:"username"`
	TokenName                 string    `json:"token_name"`
	TokenGroup                string    `json:"token_group"`
	ModelName                 string    `json:"model_name"`
	ChannelName               string    `json:"channel_name"`
	Quota                     int       `json:"quota"`
	CostQuota                 int       `json:"cost_quota"`
	PromptTokens              int       `json:"prompt_tokens"`
	CompletionTokens          int       `json:"completion_tokens"`
	ChannelID                 int       `json:"channel_id"`
	TokenKey                  string    `json:"token_key"`
	RequestDuration           int64     `json:"request_duration"`
	ResponseFirstByteDuration int64     `json:"response_first_byte_duration"`
	TotalDuration             int64     `json:"total_duration"`
	ElapsedTime               int64     `json:"elapsed_time"`
	IsStream                  bool      `json:"is_stream"`
	SystemPromptReset         bool      `json:"system_prompt_reset"`
	IP                        *string   `json:"ip,omitempty"`
	RemoteIP                  *string   `json:"remote_ip,omitempty"`
	Other                     string    `json:"other"`
	ErrorCode                 string    `json:"error_code"`
}

// NewElasticsearchLogStorage 创建Elasticsearch日志存储实例
func NewElasticsearchLogStorage(connectionString string) (LogStorage, error) {
	var hosts []string
	var username, password string

	if connectionString != "" {
		// 解析连接字符串，支持多种格式
		hosts, username, password = parseConnectionString(connectionString)
	} else {
		// 使用配置文件中的设置
		hosts = strings.Split(config.ElasticsearchHosts, ",")
		username = config.ElasticsearchUsername
		password = config.ElasticsearchPassword
	}

	if len(hosts) == 0 {
		return nil, fmt.Errorf("no Elasticsearch hosts configured")
	}

	// 清理和验证主机地址
	cleanHosts := make([]string, 0, len(hosts))
	for _, host := range hosts {
		host = strings.TrimSpace(host)
		if host != "" {
			// 确保主机地址有协议前缀
			if !strings.HasPrefix(host, "http://") && !strings.HasPrefix(host, "https://") {
				host = "http://" + host
			}
			cleanHosts = append(cleanHosts, host)
		}
	}

	if len(cleanHosts) == 0 {
		return nil, fmt.Errorf("no valid Elasticsearch hosts found")
	}

	storage := &ElasticsearchLogStorage{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     90 * time.Second,
			},
		},
		hosts:        cleanHosts,
		username:     username,
		password:     password,
		indexName:    config.ElasticsearchIndex,
		batchLogs:    make([]*Log, 0, config.LogStorageBatchSize),
		stopCh:       make(chan struct{}),
		healthStatus: make(map[string]bool),
		retryConfig:  DefaultRetryConfig(),

		// 批量写入优化
		batchCh:      make(chan *Log, config.LogStorageBatchSize*2),      // 缓冲区大小为批次大小的2倍
		backpressure: make(chan struct{}, config.LogStorageBatchSize*10), // 背压控制
		metrics:      BatchMetrics{},

		// 查询优化已移除缓存

		// 监控指标
		storageMetrics: StorageMetrics{
			TotalHosts: len(cleanHosts),
		},
	}

	// 初始化健康状态
	for _, host := range cleanHosts {
		storage.healthStatus[host] = true
	}

	// 执行初始健康检查
	if err := storage.performHealthCheck(); err != nil {
		logger.SysError("Elasticsearch initial health check failed: " + err.Error())
		// 不返回错误，允许在部分主机不可用的情况下继续运行
	}

	// 创建索引
	if err := storage.createIndex(); err != nil {
		return nil, fmt.Errorf("failed to create index: %v", err)
	}

	// 检查并更新索引映射（处理id字段类型变更）
	if err := storage.checkAndUpdateIndexMapping(); err != nil {
		logger.SysError("Failed to update index mapping: " + err.Error())
		// 不返回错误，允许继续运行
	}

	// 启动批量写入协程
	if config.LogStorageAsyncWrite {
		storage.wg.Add(1)
		go storage.batchWriterV2()
	}

	// 启动健康检查协程
	storage.wg.Add(1)
	go storage.healthChecker()

	// 初始化ID生成器
	if err := storage.initializeIDGenerators(); err != nil {
		logger.SysError("Failed to initialize ID generators: " + err.Error())
		// 即使出错也要尝试从ES获取最大ID，而不是使用时间戳
		logger.SysLog("Attempting to initialize ID generators with fallback logic...")
		storage.initializeIDGeneratorsWithFallback()
	}

	logger.SysLog(fmt.Sprintf("Elasticsearch storage initialized with %d hosts: %v", len(cleanHosts), cleanHosts))
	return storage, nil
}

// performHealthCheck 执行健康检查
func (e *ElasticsearchLogStorage) performHealthCheck() error {
	var lastErr error
	healthyCount := 0

	e.healthMu.Lock()
	defer e.healthMu.Unlock()

	for _, host := range e.hosts {
		url := strings.TrimSuffix(host, "/") + "/_cluster/health"
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			e.healthStatus[host] = false
			lastErr = err
			continue
		}

		if e.username != "" && e.password != "" {
			req.SetBasicAuth(e.username, e.password)
		}

		// 使用较短的超时时间进行健康检查
		client := &http.Client{Timeout: 5 * time.Second}
		resp, err := client.Do(req)
		if err != nil {
			e.healthStatus[host] = false
			lastErr = err
			continue
		}
		resp.Body.Close()

		if resp.StatusCode == 200 {
			e.healthStatus[host] = true
			healthyCount++
		} else {
			e.healthStatus[host] = false
			lastErr = fmt.Errorf("health check failed with status: %d", resp.StatusCode)
		}
	}

	// 更新健康指标
	e.storageMetrics.UpdateHealthMetrics(healthyCount, len(e.hosts))

	if healthyCount == 0 {
		return fmt.Errorf("no healthy Elasticsearch hosts available, last error: %v", lastErr)
	}

	logger.SysLog(fmt.Sprintf("Elasticsearch health check: %d/%d hosts healthy", healthyCount, len(e.hosts)))
	return nil
}

// healthChecker 健康检查协程
func (e *ElasticsearchLogStorage) healthChecker() {
	defer e.wg.Done()
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := e.performHealthCheck(); err != nil {
				logger.SysError("Elasticsearch health check failed: " + err.Error())
			}
		case <-e.stopCh:
			return
		}
	}
}

// getHealthyHosts 获取健康的主机列表
func (e *ElasticsearchLogStorage) getHealthyHosts() []string {
	e.healthMu.RLock()
	defer e.healthMu.RUnlock()

	var healthyHosts []string
	for host, isHealthy := range e.healthStatus {
		if isHealthy {
			healthyHosts = append(healthyHosts, host)
		}
	}

	// 如果没有健康的主机，返回所有主机（允许重试）
	if len(healthyHosts) == 0 {
		return e.hosts
	}

	return healthyHosts
}

// createIndex 创建Elasticsearch索引和模板
func (e *ElasticsearchLogStorage) createIndex() error {
	// 首先创建索引模板
	if err := e.createIndexTemplate(); err != nil {
		logger.SysError("Failed to create index template: " + err.Error())
		// 继续执行，不阻止索引创建
	}

	// 检查索引是否已存在
	exists, err := e.indexExists(e.indexName)
	if err != nil {
		return fmt.Errorf("failed to check index existence: %v", err)
	}

	if exists {
		logger.SysLog(fmt.Sprintf("Index %s already exists", e.indexName))
		return nil
	}

	// 创建索引
	indexMapping := e.buildIndexMapping()
	err = e.makeRequest("PUT", "/"+e.indexName, indexMapping, nil)
	if err != nil {
		return fmt.Errorf("failed to create index: %v", err)
	}

	logger.SysLog(fmt.Sprintf("Successfully created Elasticsearch index: %s", e.indexName))
	return nil
}

// checkAndUpdateIndexMapping 检查并更新索引映射
func (e *ElasticsearchLogStorage) checkAndUpdateIndexMapping() error {
	// 获取当前索引映射
	var mappingResult map[string]interface{}
	err := e.makeRequest("GET", "/"+e.indexName+"/_mapping", nil, &mappingResult)
	if err != nil {
		if strings.Contains(err.Error(), "404") {
			// 索引不存在，无需更新
			return nil
		}
		return fmt.Errorf("failed to get current mapping: %v", err)
	}

	// 检查id字段的类型
	if indexMapping, ok := mappingResult[e.indexName].(map[string]interface{}); ok {
		if mappings, ok := indexMapping["mappings"].(map[string]interface{}); ok {
			if properties, ok := mappings["properties"].(map[string]interface{}); ok {
				if idField, ok := properties["id"].(map[string]interface{}); ok {
					if fieldType, ok := idField["type"].(string); ok && fieldType == "keyword" {
						logger.SysLog("Detected id field as keyword type, need to reindex with integer type")
						// 如果id字段是keyword类型，需要重建索引
						return e.reindexWithNewMapping()
					}
				}
			}
		}
	}

	return nil
}

// reindexWithNewMapping 使用新映射重建索引
func (e *ElasticsearchLogStorage) reindexWithNewMapping() error {
	tempIndexName := e.indexName + "-temp-" + fmt.Sprintf("%d", time.Now().Unix())

	logger.SysLog(fmt.Sprintf("Starting reindex from %s to %s with new mapping", e.indexName, tempIndexName))

	// 1. 创建临时索引，使用新的映射
	indexMapping := e.buildIndexMapping()
	err := e.makeRequest("PUT", "/"+tempIndexName, indexMapping, nil)
	if err != nil {
		return fmt.Errorf("failed to create temp index: %v", err)
	}

	// 2. 重建索引数据
	reindexBody := map[string]interface{}{
		"source": map[string]interface{}{
			"index": e.indexName,
		},
		"dest": map[string]interface{}{
			"index": tempIndexName,
		},
		"script": map[string]interface{}{
			"source": "ctx._source.id = Integer.parseInt(ctx._source.id.toString())",
			"lang":   "painless",
		},
	}

	var reindexResult map[string]interface{}
	err = e.makeRequest("POST", "/_reindex?wait_for_completion=true", reindexBody, &reindexResult)
	if err != nil {
		// 清理临时索引
		e.makeRequest("DELETE", "/"+tempIndexName, nil, nil)
		return fmt.Errorf("failed to reindex: %v", err)
	}

	// 3. 删除原索引
	err = e.makeRequest("DELETE", "/"+e.indexName, nil, nil)
	if err != nil {
		logger.SysError("Failed to delete original index: " + err.Error())
		// 继续执行，不返回错误
	}

	// 4. 将临时索引重命名为原索引名
	aliasBody := map[string]interface{}{
		"actions": []map[string]interface{}{
			{
				"add": map[string]interface{}{
					"index": tempIndexName,
					"alias": e.indexName,
				},
			},
		},
	}

	err = e.makeRequest("POST", "/_aliases", aliasBody, nil)
	if err != nil {
		return fmt.Errorf("failed to create alias: %v", err)
	}

	logger.SysLog(fmt.Sprintf("Successfully reindexed %s with new mapping", e.indexName))
	return nil
}

// createIndexTemplate 创建索引模板
func (e *ElasticsearchLogStorage) createIndexTemplate() error {
	templateName := e.indexName + "-template"

	template := map[string]interface{}{
		"index_patterns": []string{e.indexName + "*"},
		"template": map[string]interface{}{
			"settings": e.buildIndexSettings(),
			"mappings": e.buildIndexMappings(),
		},
		"priority": 100,
		"version":  1,
		"_meta": map[string]interface{}{
			"description": "Template for shell-api logs",
			"created_by":  "shell-api",
		},
	}

	return e.makeRequest("PUT", "/_index_template/"+templateName, template, nil)
}

// indexExists 检查索引是否存在
func (e *ElasticsearchLogStorage) indexExists(indexName string) (bool, error) {
	err := e.makeRequestWithRetry("HEAD", "/"+indexName, nil, nil, 1)
	if err != nil {
		if strings.Contains(err.Error(), "404") {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// buildIndexSettings 构建索引设置
func (e *ElasticsearchLogStorage) buildIndexSettings() map[string]interface{} {
	settings := map[string]interface{}{
		"number_of_shards":   config.ElasticsearchShards,
		"number_of_replicas": config.ElasticsearchReplicas,
		"refresh_interval":   config.ElasticsearchRefreshInterval,
		"max_result_window":  50000, // 增加最大结果窗口
		"analysis": map[string]interface{}{
			"analyzer": map[string]interface{}{
				"log_analyzer": map[string]interface{}{
					"type":      "custom",
					"tokenizer": "standard",
					"filter":    []string{"lowercase", "stop"},
				},
			},
		},
		"index": map[string]interface{}{
			"sort": map[string]interface{}{
				"field": []string{"created_at"},
				"order": []string{"desc"},
			},
		},
	}

	// 如果启用了压缩，添加压缩设置
	if config.LogStorageCompressionEnabled {
		settings["codec"] = "best_compression"
	}

	return settings
}

// buildIndexMappings 构建索引映射
func (e *ElasticsearchLogStorage) buildIndexMappings() map[string]interface{} {
	return map[string]interface{}{
		"properties": e.buildFieldMappings(),
		"dynamic":    "strict", // 严格模式，不允许动态字段
	}
}

// buildIndexMapping 构建完整的索引映射（用于直接创建索引）
func (e *ElasticsearchLogStorage) buildIndexMapping() map[string]interface{} {
	return map[string]interface{}{
		"settings": e.buildIndexSettings(),
		"mappings": e.buildIndexMappings(),
	}
}

// buildFieldMappings 构建字段映射
func (e *ElasticsearchLogStorage) buildFieldMappings() map[string]interface{} {
	return map[string]interface{}{
		"id": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"request_id": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"user_id": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"created_at": map[string]interface{}{
			"type":   "date",
			"format": "strict_date_optional_time||epoch_second",
			"index":  true,
		},
		"type": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"content": map[string]interface{}{
			"type":     "text",
			"analyzer": "log_analyzer",
			"fields": map[string]interface{}{
				"keyword": map[string]interface{}{
					"type":         "keyword",
					"ignore_above": 256,
				},
			},
		},
		"username": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"token_name": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"token_group": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"model_name": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"channel_name": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"quota": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"cost_quota": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"prompt_tokens": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"completion_tokens": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"channel_id": map[string]interface{}{
			"type":  "integer",
			"index": true,
		},
		"token_key": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
		"request_duration": map[string]interface{}{
			"type":  "long",
			"index": true,
		},
		"response_first_byte_duration": map[string]interface{}{
			"type":  "long",
			"index": true,
		},
		"total_duration": map[string]interface{}{
			"type":  "long",
			"index": true,
		},
		"elapsed_time": map[string]interface{}{
			"type":  "long",
			"index": true,
		},
		"is_stream": map[string]interface{}{
			"type":  "boolean",
			"index": true,
		},
		"system_prompt_reset": map[string]interface{}{
			"type":  "boolean",
			"index": true,
		},
		"ip": map[string]interface{}{
			"type":  "ip",
			"index": true,
		},
		"remote_ip": map[string]interface{}{
			"type":  "ip",
			"index": true,
		},
		"other": map[string]interface{}{
			"type":     "text",
			"analyzer": "log_analyzer",
			"index":    false, // 不索引，只存储
		},
		"error_code": map[string]interface{}{
			"type":  "keyword",
			"index": true,
		},
	}
}

// createIndexLifecyclePolicy 创建索引生命周期策略
func (e *ElasticsearchLogStorage) createIndexLifecyclePolicy() error {
	policyName := e.indexName + "-policy"

	policy := map[string]interface{}{
		"policy": map[string]interface{}{
			"phases": map[string]interface{}{
				"hot": map[string]interface{}{
					"actions": map[string]interface{}{
						"rollover": map[string]interface{}{
							"max_size": "10GB",
							"max_age":  "7d",
						},
						"set_priority": map[string]interface{}{
							"priority": 100,
						},
					},
				},
				"warm": map[string]interface{}{
					"min_age": "7d",
					"actions": map[string]interface{}{
						"set_priority": map[string]interface{}{
							"priority": 50,
						},
						"allocate": map[string]interface{}{
							"number_of_replicas": 0,
						},
						"forcemerge": map[string]interface{}{
							"max_num_segments": 1,
						},
					},
				},
				"cold": map[string]interface{}{
					"min_age": "30d",
					"actions": map[string]interface{}{
						"set_priority": map[string]interface{}{
							"priority": 0,
						},
						"allocate": map[string]interface{}{
							"number_of_replicas": 0,
						},
					},
				},
				"delete": map[string]interface{}{
					"min_age": "90d",
					"actions": map[string]interface{}{
						"delete": map[string]interface{}{},
					},
				},
			},
		},
	}

	return e.makeRequest("PUT", "/_ilm/policy/"+policyName, policy, nil)
}

// getTimeBasedIndexName 获取基于时间的索引名称
func (e *ElasticsearchLogStorage) getTimeBasedIndexName(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	return fmt.Sprintf("%s-%s", e.indexName, t.Format("2006.01.02"))
}

// ensureTimeBasedIndex 确保基于时间的索引存在
func (e *ElasticsearchLogStorage) ensureTimeBasedIndex(timestamp int64) error {
	indexName := e.getTimeBasedIndexName(timestamp)

	exists, err := e.indexExists(indexName)
	if err != nil {
		return err
	}

	if !exists {
		indexMapping := e.buildIndexMapping()
		err = e.makeRequest("PUT", "/"+indexName, indexMapping, nil)
		if err != nil {
			return fmt.Errorf("failed to create time-based index %s: %v", indexName, err)
		}
		logger.SysLog(fmt.Sprintf("Created time-based index: %s", indexName))
	}

	return nil
}

// batchWriter 批量写入协程（旧版本，保留兼容性）
func (e *ElasticsearchLogStorage) batchWriter() {
	defer e.wg.Done()
	ticker := time.NewTicker(time.Duration(config.LogStorageFlushInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			e.flushBatch()
		case <-e.stopCh:
			e.flushBatch() // 最后一次刷新
			return
		}
	}
}

// batchWriterV2 改进的批量写入协程，使用通道和背压控制
func (e *ElasticsearchLogStorage) batchWriterV2() {
	defer e.wg.Done()

	batch := make([]*Log, 0, config.LogStorageBatchSize)
	ticker := time.NewTicker(time.Duration(config.LogStorageFlushInterval) * time.Second)
	defer ticker.Stop()

	flushBatch := func() {
		if len(batch) == 0 {
			return
		}

		startTime := time.Now()
		batchSize := len(batch)

		ctx := context.Background()
		err := e.RecordLogBatch(ctx, batch)

		flushTime := time.Since(startTime)
		success := err == nil

		// 更新指标
		e.metrics.UpdateBatchMetrics(batchSize, flushTime, success)

		if err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to flush batch of %d logs: %v", batchSize, err))
		} else {
			logger.Infof(ctx, "successfully flushed batch of %d logs in %v", batchSize, flushTime)
		}

		// 清空批次
		batch = batch[:0]

		// 释放背压
		for i := 0; i < batchSize && len(e.backpressure) > 0; i++ {
			select {
			case <-e.backpressure:
			default:
			}
		}
	}

	for {
		select {
		case log := <-e.batchCh:
			batch = append(batch, log)

			// 如果批次满了，立即刷新
			if len(batch) >= config.LogStorageBatchSize {
				flushBatch()
			}

		case <-ticker.C:
			// 定时刷新
			flushBatch()

		case <-e.stopCh:
			// 处理剩余的日志
			for {
				select {
				case log := <-e.batchCh:
					batch = append(batch, log)
				default:
					goto drain_complete
				}
			}
		drain_complete:
			flushBatch() // 最后一次刷新
			return
		}
	}
}

// flushBatch 刷新批次
func (e *ElasticsearchLogStorage) flushBatch() {
	e.batchMu.Lock()
	if len(e.batchLogs) == 0 {
		e.batchMu.Unlock()
		return
	}

	logs := make([]*Log, len(e.batchLogs))
	copy(logs, e.batchLogs)
	e.batchLogs = e.batchLogs[:0] // 清空但保留容量
	e.batchMu.Unlock()

	ctx := context.Background()
	if err := e.RecordLogBatch(ctx, logs); err != nil {
		logger.Error(ctx, "failed to flush batch logs to Elasticsearch: "+err.Error())
	} else {
		logger.Infof(ctx, "flushed %d logs to Elasticsearch", len(logs))
	}
}

// makeRequest 发送HTTP请求到Elasticsearch，带重试机制
func (e *ElasticsearchLogStorage) makeRequest(method, path string, body interface{}, result interface{}) error {
	return e.makeRequestWithRetry(method, path, body, result, e.retryConfig.MaxRetries)
}

// makeRequestWithRetry 发送HTTP请求到Elasticsearch，支持自定义重试次数（带监控）
func (e *ElasticsearchLogStorage) makeRequestWithRetry(method, path string, body interface{}, result interface{}, maxRetries int) error {
	startTime := time.Now()
	var reqBodyBytes []byte
	var err error

	if body != nil {
		reqBodyBytes, err = json.Marshal(body)
		if err != nil {
			e.storageMetrics.UpdateRequestMetrics(time.Since(startTime), false)
			return fmt.Errorf("failed to marshal request body: %v", err)
		}
	}

	// 获取健康的主机列表
	healthyHosts := e.getHealthyHosts()
	if len(healthyHosts) == 0 {
		e.storageMetrics.UpdateRequestMetrics(time.Since(startTime), false)
		return fmt.Errorf("no healthy Elasticsearch hosts available")
	}

	var lastErr error
	delay := e.retryConfig.InitialDelay

	for attempt := 0; attempt <= maxRetries; attempt++ {
		// 如果不是第一次尝试，等待一段时间
		if attempt > 0 {
			time.Sleep(delay)
			// 指数退避
			delay = time.Duration(float64(delay) * e.retryConfig.BackoffFactor)
			if delay > e.retryConfig.MaxDelay {
				delay = e.retryConfig.MaxDelay
			}

			// 重新获取健康主机列表
			healthyHosts = e.getHealthyHosts()
		}

		// 尝试所有健康的主机
		for _, host := range healthyHosts {
			var reqBody io.Reader
			if reqBodyBytes != nil {
				reqBody = bytes.NewReader(reqBodyBytes)
			}

			url := strings.TrimSuffix(host, "/") + path
			req, err := http.NewRequest(method, url, reqBody)
			if err != nil {
				lastErr = fmt.Errorf("failed to create request for %s: %v", host, err)
				continue
			}

			req.Header.Set("Content-Type", "application/json")
			if e.username != "" && e.password != "" {
				req.SetBasicAuth(e.username, e.password)
			}

			requestStartTime := time.Now()
			resp, err := e.client.Do(req)
			requestDuration := time.Since(requestStartTime)

			if err != nil {
				lastErr = fmt.Errorf("request failed for %s: %v", host, err)
				// 标记主机为不健康
				e.markHostUnhealthy(host)
				continue
			}

			// 检查响应状态
			if resp.StatusCode >= 500 {
				// 服务器错误，可以重试
				bodyBytes, _ := io.ReadAll(resp.Body)
				resp.Body.Close()
				lastErr = fmt.Errorf("server error from %s: %d %s", host, resp.StatusCode, string(bodyBytes))
				e.markHostUnhealthy(host)
				e.storageMetrics.UpdateRequestMetrics(requestDuration, false)
				continue
			} else if resp.StatusCode >= 400 {
				// 客户端错误，通常不需要重试
				bodyBytes, _ := io.ReadAll(resp.Body)
				resp.Body.Close()
				e.storageMetrics.UpdateRequestMetrics(requestDuration, false)
				return fmt.Errorf("client error from %s: %d %s", host, resp.StatusCode, string(bodyBytes))
			}

			// 成功响应
			defer resp.Body.Close()
			if result != nil {
				err = json.NewDecoder(resp.Body).Decode(result)
				if err != nil {
					e.storageMetrics.UpdateRequestMetrics(requestDuration, false)
					return fmt.Errorf("failed to decode response from %s: %v", host, err)
				}
			}

			// 更新成功指标
			e.storageMetrics.UpdateRequestMetrics(requestDuration, true)
			return nil
		}
	}

	e.storageMetrics.UpdateRequestMetrics(time.Since(startTime), false)
	return fmt.Errorf("all elasticsearch hosts failed after %d retries, last error: %v", maxRetries, lastErr)
}

// markHostUnhealthy 标记主机为不健康
func (e *ElasticsearchLogStorage) markHostUnhealthy(host string) {
	e.healthMu.Lock()
	defer e.healthMu.Unlock()
	e.healthStatus[host] = false
}

// RecordLog 记录单条日志
func (e *ElasticsearchLogStorage) RecordLog(ctx context.Context, log *Log) error {
	if config.LogStorageAsyncWrite {
		// 使用改进的异步写入
		return e.recordLogAsync(ctx, log)
	} else {
		// 同步写入
		return e.indexLog(ctx, log)
	}
}

// recordLogAsync 异步记录日志，带背压控制
func (e *ElasticsearchLogStorage) recordLogAsync(ctx context.Context, log *Log) error {
	// 检查背压
	select {
	case e.backpressure <- struct{}{}:
		// 成功获取背压令牌
	default:
		// 背压满了，记录指标并降级到同步写入
		e.metrics.IncrementBackpressure()
		logger.SysError("Elasticsearch batch writer backpressure full, falling back to sync write")
		return e.indexLog(ctx, log)
	}

	// 尝试发送到批量写入通道
	select {
	case e.batchCh <- log:
		return nil
	case <-ctx.Done():
		// 释放背压令牌
		select {
		case <-e.backpressure:
		default:
		}
		return ctx.Err()
	default:
		// 通道满了，释放背压令牌并降级到同步写入
		select {
		case <-e.backpressure:
		default:
		}
		e.metrics.IncrementBackpressure()
		logger.SysError("Elasticsearch batch channel full, falling back to sync write")
		return e.indexLog(ctx, log)
	}
}

// indexLog 索引单条日志
func (e *ElasticsearchLogStorage) indexLog(ctx context.Context, log *Log) error {
	if log.RequestId == "" {
		log.RequestId = helper.GetRequestID(ctx)
	}

	// 如果业务ID为0，生成新的业务ID
	if log.Id == 0 {
		log.Id = e.logIDGenerator.NextID()
	}

	esLog := e.convertToElasticsearchLog(log)

	// 让 Elasticsearch 自动生成文档 _id，但业务数据中包含我们的业务 ID
	return e.makeRequest("POST", "/"+e.indexName+"/_doc", esLog, nil)
}

// convertToElasticsearchLog 将Log转换为ElasticsearchLog
func (e *ElasticsearchLogStorage) convertToElasticsearchLog(log *Log) *ElasticsearchLog {
	// 处理IP字段，空字符串转换为nil以避免Elasticsearch ip类型字段的解析错误
	var ip, remoteIP *string
	if log.Ip != "" {
		ip = &log.Ip
	}
	if log.RemoteIp != "" {
		remoteIP = &log.RemoteIp
	}

	return &ElasticsearchLog{
		ID:                        strconv.FormatInt(int64(log.Id), 10), // 业务ID
		RequestID:                 log.RequestId,
		UserID:                    log.UserId,
		CreatedAt:                 time.Unix(log.CreatedAt, 0),
		Type:                      log.Type,
		Content:                   log.Content,
		Username:                  log.Username,
		TokenName:                 log.TokenName,
		TokenGroup:                log.TokenGroup,
		ModelName:                 log.ModelName,
		ChannelName:               log.ChannelName,
		Quota:                     log.Quota,
		CostQuota:                 log.CostQuota,
		PromptTokens:              log.PromptTokens,
		CompletionTokens:          log.CompletionTokens,
		ChannelID:                 log.ChannelId,
		TokenKey:                  log.TokenKey,
		RequestDuration:           log.RequestDuration,
		ResponseFirstByteDuration: log.ResponseFirstByteDuration,
		TotalDuration:             log.TotalDuration,
		ElapsedTime:               log.ElapsedTime,
		IsStream:                  log.IsStream,
		SystemPromptReset:         log.SystemPromptReset,
		IP:                        ip,
		RemoteIP:                  remoteIP,
		Other:                     log.Other,
		ErrorCode:                 log.ErrorCode,
	}
}

// RecordLogBatch 批量记录日志
func (e *ElasticsearchLogStorage) RecordLogBatch(ctx context.Context, logs []*Log) error {
	if len(logs) == 0 {
		return nil
	}

	// 构建批量请求
	var bulkBody strings.Builder
	for _, log := range logs {
		if log.RequestId == "" {
			log.RequestId = helper.GetRequestID(ctx)
		}

		// 如果业务ID为0，生成新的业务ID
		if log.Id == 0 {
			log.Id = e.logIDGenerator.NextID()
		}

		esLog := e.convertToElasticsearchLog(log)

		// 让 Elasticsearch 自动生成文档 _id，不指定 _id
		indexAction := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": e.indexName,
				// 不指定 _id，让 ES 自动生成文档ID
			},
		}

		actionBytes, _ := json.Marshal(indexAction)
		bulkBody.Write(actionBytes)
		bulkBody.WriteString("\n")

		// 文档数据
		docBytes, _ := json.Marshal(esLog)
		bulkBody.Write(docBytes)
		bulkBody.WriteString("\n")
	}

	// 发送批量请求
	return e.makeRequestRaw("POST", "/_bulk", bulkBody.String(), nil)
}

// RecordConsumeLogByDetailIfZeroQuota 记录消费日志（如果配额为零）
func (e *ElasticsearchLogStorage) RecordConsumeLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, quota int, costQuota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64,
	isStream bool, content string, other string) (*Log, error) {

	if ctx == nil {
		ctx = context.Background()
	}

	// 获取用户信息
	user, _ := GetUserById(userId, false)
	var username string
	if user != nil {
		username = user.Username
	}

	log := &Log{
		UserId:                    userId,
		Username:                  username,
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeConsume,
		Content:                   content,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		TokenGroup:                tokenGroup,
		ModelName:                 modelName,
		Quota:                     quota,
		CostQuota:                 costQuota,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		ChannelId:                 channelId,
		ChannelName:               channelName,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		Other:                     other,
	}

	// 使用现有的 RecordLog 方法
	err := e.RecordLog(ctx, log)
	return log, err
}

// makeRequestRaw 发送原始字符串请求（改进版本，带监控和重试）
func (e *ElasticsearchLogStorage) makeRequestRaw(method, path, body string, result interface{}) error {
	startTime := time.Now()

	// 获取健康的主机列表
	healthyHosts := e.getHealthyHosts()
	if len(healthyHosts) == 0 {
		e.storageMetrics.UpdateRequestMetrics(time.Since(startTime), false)
		return fmt.Errorf("no healthy Elasticsearch hosts available")
	}

	var lastErr error
	delay := e.retryConfig.InitialDelay

	for attempt := 0; attempt <= e.retryConfig.MaxRetries; attempt++ {
		// 如果不是第一次尝试，等待一段时间
		if attempt > 0 {
			time.Sleep(delay)
			delay = time.Duration(float64(delay) * e.retryConfig.BackoffFactor)
			if delay > e.retryConfig.MaxDelay {
				delay = e.retryConfig.MaxDelay
			}
			healthyHosts = e.getHealthyHosts()
		}

		for _, host := range healthyHosts {
			url := strings.TrimSuffix(host, "/") + path
			req, err := http.NewRequest(method, url, strings.NewReader(body))
			if err != nil {
				lastErr = fmt.Errorf("failed to create request for %s: %v", host, err)
				continue
			}

			req.Header.Set("Content-Type", "application/x-ndjson")
			if e.username != "" && e.password != "" {
				req.SetBasicAuth(e.username, e.password)
			}

			requestStartTime := time.Now()
			resp, err := e.client.Do(req)
			requestDuration := time.Since(requestStartTime)

			if err != nil {
				lastErr = fmt.Errorf("request failed for %s: %v", host, err)
				e.markHostUnhealthy(host)
				continue
			}

			if resp.StatusCode >= 500 {
				// 服务器错误，可以重试
				bodyBytes, _ := io.ReadAll(resp.Body)
				resp.Body.Close()
				lastErr = fmt.Errorf("server error from %s: %d %s", host, resp.StatusCode, string(bodyBytes))
				e.markHostUnhealthy(host)
				e.storageMetrics.UpdateRequestMetrics(requestDuration, false)
				continue
			} else if resp.StatusCode >= 400 {
				// 客户端错误，通常不需要重试
				bodyBytes, _ := io.ReadAll(resp.Body)
				resp.Body.Close()
				e.storageMetrics.UpdateRequestMetrics(requestDuration, false)
				return fmt.Errorf("client error from %s: %d %s", host, resp.StatusCode, string(bodyBytes))
			}

			// 成功响应
			defer resp.Body.Close()
			if result != nil {
				err = json.NewDecoder(resp.Body).Decode(result)
				if err != nil {
					e.storageMetrics.UpdateRequestMetrics(requestDuration, false)
					return fmt.Errorf("failed to decode response from %s: %v", host, err)
				}
			}

			// 更新成功指标
			e.storageMetrics.UpdateRequestMetrics(requestDuration, true)
			return nil
		}
	}

	e.storageMetrics.UpdateRequestMetrics(time.Since(startTime), false)
	return fmt.Errorf("all elasticsearch hosts failed after %d retries, last error: %v", e.retryConfig.MaxRetries, lastErr)
}

// GetAllLogs 获取所有日志（带缓存优化）
func (e *ElasticsearchLogStorage) GetAllLogs(userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, startIdx int, num int, channel int, isStream string, requestId string,
	ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64,
	requestDurationMin *float64, requestDurationMax *float64, responseFirstByteDurationMin *float64,
	responseFirstByteDurationMax *float64, excludeModels []string, errorCode string,
	excludeErrorCodes []string, quotaMin *int, quotaMax *int) ([]*Log, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	// 移除缓存逻辑，直接查询确保数据实时性

	// 构建优化的查询
	query := e.buildOptimizedQuery(logType, startTimestamp, endTimestamp, modelName, username, tokenName,
		tokenKey, tokenGroup, channelName, channel, isStream, requestId, ip, promptTokensMin,
		promptTokensMax, completionTokensMin, completionTokensMax, totalDurationMin, totalDurationMax,
		requestDurationMin, requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
		excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)

	searchRequest := map[string]interface{}{
		"query": query,
		"sort": []map[string]interface{}{
			{"created_at": map[string]string{"order": "desc"}},
		},
		"from": startIdx,
		"size": num,
		// 添加性能优化选项
		"track_total_hits": false, // 不计算总数，提高性能
		// 移除对other字段的排除，因为该字段包含重要的业务信息
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	logs, err := e.parseSearchResults(searchResult)
	if err != nil {
		return nil, err
	}

	// 更新查询指标
	e.storageMetrics.UpdateQueryMetrics()

	return logs, nil
}

// buildQuery 构建Elasticsearch查询
func (e *ElasticsearchLogStorage) buildQuery(logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64, requestDurationMax *float64,
	responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64, excludeModels []string,
	errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) map[string]interface{} {

	must := []map[string]interface{}{}

	// 日志类型过滤
	if len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		must = append(must, map[string]interface{}{
			"terms": map[string]interface{}{
				"type": logType,
			},
		})
	}

	// 时间范围过滤
	if startTimestamp != 0 || endTimestamp != 0 {
		rangeQuery := map[string]interface{}{}
		if startTimestamp != 0 {
			rangeQuery["gte"] = time.Unix(startTimestamp, 0).Format(time.RFC3339)
		}
		if endTimestamp != 0 {
			rangeQuery["lte"] = time.Unix(endTimestamp, 0).Format(time.RFC3339)
		}
		must = append(must, map[string]interface{}{
			"range": map[string]interface{}{
				"created_at": rangeQuery,
			},
		})
	}

	// 字符串字段精确匹配
	stringFields := map[string]string{
		"model_name":   modelName,
		"username":     username,
		"token_name":   tokenName,
		"token_key":    tokenKey,
		"token_group":  tokenGroup,
		"channel_name": channelName,
		"request_id":   requestId,
		"ip":           ip,
		"error_code":   errorCode,
	}

	for field, value := range stringFields {
		if value != "" {
			must = append(must, map[string]interface{}{
				"term": map[string]interface{}{
					field: value,
				},
			})
		}
	}

	// 整数字段匹配
	if channel != 0 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"channel_id": channel,
			},
		})
	}

	// 布尔字段匹配
	if isStream != "" {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"is_stream": isStream == "true",
			},
		})
	}

	// 数值范围过滤
	numericRanges := map[string][2]*int{
		"prompt_tokens":     {promptTokensMin, promptTokensMax},
		"completion_tokens": {completionTokensMin, completionTokensMax},
		"quota":             {quotaMin, quotaMax},
	}

	for field, minMax := range numericRanges {
		min, max := minMax[0], minMax[1]
		if min != nil || max != nil {
			rangeQuery := map[string]interface{}{}
			if min != nil {
				rangeQuery["gte"] = *min
			}
			if max != nil {
				rangeQuery["lte"] = *max
			}
			must = append(must, map[string]interface{}{
				"range": map[string]interface{}{
					field: rangeQuery,
				},
			})
		}
	}

	// 排除模型
	if len(excludeModels) > 0 {
		must = append(must, map[string]interface{}{
			"bool": map[string]interface{}{
				"must_not": map[string]interface{}{
					"terms": map[string]interface{}{
						"model_name": excludeModels,
					},
				},
			},
		})
	}

	// 排除错误代码
	if len(excludeErrorCodes) > 0 {
		must = append(must, map[string]interface{}{
			"bool": map[string]interface{}{
				"must_not": map[string]interface{}{
					"terms": map[string]interface{}{
						"error_code": excludeErrorCodes,
					},
				},
			},
		})
	}

	return map[string]interface{}{
		"bool": map[string]interface{}{
			"must": must,
		},
	}
}

// parseSearchResults 解析搜索结果
func (e *ElasticsearchLogStorage) parseSearchResults(result map[string]interface{}) ([]*Log, error) {
	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid search result format")
	}

	hitsList, ok := hits["hits"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid hits format")
	}

	logs := make([]*Log, 0, len(hitsList))
	for _, hit := range hitsList {
		hitMap, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}

		source, ok := hitMap["_source"].(map[string]interface{})
		if !ok {
			continue
		}

		log, err := e.convertFromElasticsearchLog(source)
		if err != nil {
			continue // 跳过无法解析的文档
		}

		logs = append(logs, log)
	}

	return logs, nil
}

// convertFromElasticsearchLog 将Elasticsearch文档转换为Log
func (e *ElasticsearchLogStorage) convertFromElasticsearchLog(source map[string]interface{}) (*Log, error) {
	log := &Log{}

	// 辅助函数：安全地获取字符串值
	getString := func(key string) string {
		if val, ok := source[key].(string); ok {
			return val
		}
		return ""
	}

	// 辅助函数：安全地获取整数值
	getInt := func(key string) int {
		if val, ok := source[key].(float64); ok {
			return int(val)
		}
		// 也尝试从字符串解析（兼容性）
		if val, ok := source[key].(string); ok {
			if intVal, err := strconv.Atoi(val); err == nil {
				return intVal
			}
		}
		return 0
	}

	// 辅助函数：安全地获取int64值
	getInt64 := func(key string) int64 {
		if val, ok := source[key].(float64); ok {
			return int64(val)
		}
		return 0
	}

	// 辅助函数：安全地获取布尔值
	getBool := func(key string) bool {
		if val, ok := source[key].(bool); ok {
			return val
		}
		return false
	}

	// 填充字段 - 直接使用getInt获取ID
	log.Id = getInt("id")

	log.RequestId = getString("request_id")
	log.UserId = getInt("user_id")

	// 处理时间字段
	if createdAtStr := getString("created_at"); createdAtStr != "" {
		if t, err := time.Parse(time.RFC3339, createdAtStr); err == nil {
			log.CreatedAt = t.Unix()
		}
	}

	log.Type = getInt("type")
	log.Content = getString("content")
	log.Username = getString("username")
	log.TokenName = getString("token_name")
	log.TokenGroup = getString("token_group")
	log.ModelName = getString("model_name")
	log.ChannelName = getString("channel_name")
	log.Quota = getInt("quota")
	log.CostQuota = getInt("cost_quota")
	log.PromptTokens = getInt("prompt_tokens")
	log.CompletionTokens = getInt("completion_tokens")
	log.ChannelId = getInt("channel_id")
	log.TokenKey = getString("token_key")
	log.RequestDuration = getInt64("request_duration")
	log.ResponseFirstByteDuration = getInt64("response_first_byte_duration")
	log.TotalDuration = getInt64("total_duration")
	log.ElapsedTime = getInt64("elapsed_time")
	log.IsStream = getBool("is_stream")
	log.SystemPromptReset = getBool("system_prompt_reset")
	log.Ip = getString("ip")
	log.RemoteIp = getString("remote_ip")
	log.Other = getString("other")
	log.ErrorCode = getString("error_code")

	return log, nil
}

// CountAllLogs 统计所有日志数量
func (e *ElasticsearchLogStorage) CountAllLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (int64, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	query := e.buildQuery(logType, startTimestamp, endTimestamp, modelName, username, tokenName,
		tokenKey, tokenGroup, channelName, channel, isStream, requestId, ip, promptTokensMin,
		promptTokensMax, completionTokensMin, completionTokensMax, totalDurationMin, totalDurationMax,
		requestDurationMin, requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
		excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)

	countRequest := map[string]interface{}{
		"query": query,
	}

	var countResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_count", countRequest, &countResult)
	if err != nil {
		return 0, err
	}

	if count, ok := countResult["count"].(float64); ok {
		return int64(count), nil
	}

	return 0, fmt.Errorf("invalid count result format")
}

// GetUserLogs 获取用户日志
func (e *ElasticsearchLogStorage) GetUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, tokenName string, tokenKey string, startIdx int, num int,
	isStream string, requestId string) ([]*Log, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	// 对于非管理员用户，需要过滤掉系统日志和下游错误日志
	if !IsAdmin(userId) {
		excludeTypes := []int{6, 7, 8, 15, 16} // LogTypeSystemInfo, LogTypeSystemWarn, LogTypeSystemErr, LogTypeDownstreamError, LogTypeTest
		if logType == nil || len(logType) == 0 || (len(logType) == 1 && logType[0] == 0) {
			// 如果没有指定日志类型，则排除系统日志类型
			logType = []int{}
			for i := 0; i <= 20; i++ { // 假设最大日志类型为20
				excluded := false
				for _, et := range excludeTypes {
					if i == et {
						excluded = true
						break
					}
				}
				if !excluded {
					logType = append(logType, i)
				}
			}
		} else {
			// 如果指定了日志类型，则从中排除系统日志类型
			filteredLogType := []int{}
			for _, lt := range logType {
				excluded := false
				for _, et := range excludeTypes {
					if lt == et {
						excluded = true
						break
					}
				}
				if !excluded {
					filteredLogType = append(filteredLogType, lt)
				}
			}
			logType = filteredLogType
		}
	}

	query := e.buildQuery(logType, startTimestamp, endTimestamp, modelName, "", tokenName,
		tokenKey, "", "", 0, isStream, requestId, "", nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		[]string{}, "", []string{}, nil, nil)

	// 添加用户ID过滤
	if boolQuery, ok := query["bool"].(map[string]interface{}); ok {
		if must, ok := boolQuery["must"].([]map[string]interface{}); ok {
			must = append(must, map[string]interface{}{
				"term": map[string]interface{}{
					"user_id": userId,
				},
			})
			boolQuery["must"] = must
		}
	}

	searchRequest := map[string]interface{}{
		"query": query,
		"sort": []map[string]interface{}{
			{"created_at": map[string]string{"order": "desc"}},
		},
		"from": startIdx,
		"size": num,
	}

	// 对于非管理员用户，排除敏感字段
	if !IsAdmin(userId) {
		searchRequest["_source"] = map[string]interface{}{
			"excludes": []string{"id", "channel_name", "channel_id", "cost_quota", "error_code"},
		}
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	logs, err := e.parseSearchResults(searchResult)
	if err != nil {
		return nil, err
	}

	// 对于非管理员用户，确保敏感字段被清除
	if !IsAdmin(userId) {
		for _, log := range logs {
			log.Id = 0
			log.ChannelName = ""
			log.ChannelId = 0
			log.CostQuota = 0
			log.ErrorCode = ""
		}
	}

	return logs, nil
}

// CountUserLogs 统计用户日志数量
func (e *ElasticsearchLogStorage) CountUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, tokenName string, tokenKey string, isStream string, requestId string) (int64, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	// 对于非管理员用户，需要过滤掉系统日志和下游错误日志
	if !IsAdmin(userId) {
		excludeTypes := []int{6, 7, 8, 15, 16} // LogTypeSystemInfo, LogTypeSystemWarn, LogTypeSystemErr, LogTypeDownstreamError, LogTypeTest
		if logType == nil || len(logType) == 0 || (len(logType) == 1 && logType[0] == 0) {
			// 如果没有指定日志类型，则排除系统日志类型
			logType = []int{}
			for i := 0; i <= 20; i++ { // 假设最大日志类型为20
				excluded := false
				for _, et := range excludeTypes {
					if i == et {
						excluded = true
						break
					}
				}
				if !excluded {
					logType = append(logType, i)
				}
			}
		} else {
			// 如果指定了日志类型，则从中排除系统日志类型
			filteredLogType := []int{}
			for _, lt := range logType {
				excluded := false
				for _, et := range excludeTypes {
					if lt == et {
						excluded = true
						break
					}
				}
				if !excluded {
					filteredLogType = append(filteredLogType, lt)
				}
			}
			logType = filteredLogType
		}
	}

	query := e.buildQuery(logType, startTimestamp, endTimestamp, modelName, "", tokenName,
		tokenKey, "", "", 0, isStream, requestId, "", nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		[]string{}, "", []string{}, nil, nil)

	// 添加用户ID过滤
	if boolQuery, ok := query["bool"].(map[string]interface{}); ok {
		if must, ok := boolQuery["must"].([]map[string]interface{}); ok {
			must = append(must, map[string]interface{}{
				"term": map[string]interface{}{
					"user_id": userId,
				},
			})
			boolQuery["must"] = must
		}
	}

	countRequest := map[string]interface{}{
		"query": query,
	}

	var countResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_count", countRequest, &countResult)
	if err != nil {
		return 0, err
	}

	if count, ok := countResult["count"].(float64); ok {
		return int64(count), nil
	}

	return 0, fmt.Errorf("invalid count result format")
}

// SearchAllLogs 搜索所有日志
func (e *ElasticsearchLogStorage) SearchAllLogs(keyword string) ([]*Log, error) {
	searchRequest := map[string]interface{}{
		"query": map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query":  keyword,
				"fields": []string{"content", "username", "model_name", "token_name", "error_code"},
			},
		},
		"sort": []map[string]interface{}{
			{"created_at": map[string]string{"order": "desc"}},
		},
		"size": config.MaxRecentItems,
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	return e.parseSearchResults(searchResult)
}

// SearchUserLogs 搜索用户日志
func (e *ElasticsearchLogStorage) SearchUserLogs(userId int, keyword string) ([]*Log, error) {
	mustQueries := []map[string]interface{}{
		{
			"term": map[string]interface{}{
				"user_id": userId,
			},
		},
	}

	// 添加关键词搜索条件
	if keyword != "" {
		mustQueries = append(mustQueries, map[string]interface{}{
			"multi_match": map[string]interface{}{
				"query":  keyword,
				"fields": []string{"content", "model_name", "token_name", "error_code"},
			},
		})
	}

	// 对于非管理员用户，需要过滤掉系统日志和下游错误日志
	if !IsAdmin(userId) {
		excludeTypes := []int{6, 7, 8, 15, 16} // LogTypeSystemInfo, LogTypeSystemWarn, LogTypeSystemErr, LogTypeDownstreamError, LogTypeTest
		mustNotQueries := []map[string]interface{}{}
		for _, t := range excludeTypes {
			mustNotQueries = append(mustNotQueries, map[string]interface{}{
				"term": map[string]interface{}{
					"type": t,
				},
			})
		}

		searchRequest := map[string]interface{}{
			"query": map[string]interface{}{
				"bool": map[string]interface{}{
					"must":     mustQueries,
					"must_not": mustNotQueries,
				},
			},
			"sort": []map[string]interface{}{
				{"created_at": map[string]string{"order": "desc"}},
			},
			"size": config.MaxRecentItems,
			"_source": map[string]interface{}{
				"excludes": []string{"id", "channel_name", "channel_id", "cost_quota", "error_code"},
			},
		}

		var searchResult map[string]interface{}
		err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
		if err != nil {
			return nil, err
		}

		logs, err := e.parseSearchResults(searchResult)
		if err != nil {
			return nil, err
		}

		// 确保敏感字段被清除
		for _, log := range logs {
			log.Id = 0
			log.ChannelName = ""
			log.ChannelId = 0
			log.CostQuota = 0
			log.ErrorCode = ""
		}

		return logs, nil
	} else {
		// 管理员用户，不需要过滤
		searchRequest := map[string]interface{}{
			"query": map[string]interface{}{
				"bool": map[string]interface{}{
					"must": mustQueries,
				},
			},
			"sort": []map[string]interface{}{
				{"created_at": map[string]string{"order": "desc"}},
			},
			"size": config.MaxRecentItems,
		}

		var searchResult map[string]interface{}
		err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
		if err != nil {
			return nil, err
		}

		return e.parseSearchResults(searchResult)
	}
}

// SearchUserLogsByKey 根据key搜索用户日志
func (e *ElasticsearchLogStorage) SearchUserLogsByKey(key string, startIdx int, num int) ([]*Log, error) {
	// 处理token key格式
	if key != "" {
		if strings.HasPrefix(key, "sk-") && len(strings.Split(key, "-")[1]) > 1 {
			key = strings.Split(key, "-")[1]
		}
	}

	searchRequest := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": []map[string]interface{}{
					{
						"term": map[string]interface{}{
							"token_key": key,
						},
					},
					{
						"term": map[string]interface{}{
							"type": 2, // LogTypeConsume
						},
					},
				},
			},
		},
		"sort": []map[string]interface{}{
			{"created_at": map[string]string{"order": "desc"}},
		},
		"from": startIdx,
		"size": num,
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	return e.parseSearchResults(searchResult)
}

// CountUserLogsByKey 根据key统计用户日志数量
func (e *ElasticsearchLogStorage) CountUserLogsByKey(key string) (int64, error) {
	// 处理token key格式
	if key != "" {
		if strings.HasPrefix(key, "sk-") && len(strings.Split(key, "-")[1]) > 1 {
			key = strings.Split(key, "-")[1]
		}
	}

	countRequest := map[string]interface{}{
		"query": map[string]interface{}{
			"term": map[string]interface{}{
				"token_key": key,
			},
		},
	}

	var countResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_count", countRequest, &countResult)
	if err != nil {
		return 0, err
	}

	if count, ok := countResult["count"].(float64); ok {
		return int64(count), nil
	}

	return 0, fmt.Errorf("invalid count result format")
}

// SumUsedQuota 统计使用的配额
func (e *ElasticsearchLogStorage) SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, modelName string,
	username string, tokenName string, tokenKey string, tokenGroup string, channel int, useRedis bool) (Stat, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	var stat Stat

	// 构建聚合查询 - 用于计算总配额
	quotaQuery := map[string]interface{}{
		"bool": map[string]interface{}{
			"must": []map[string]interface{}{
				{
					"terms": map[string]interface{}{
						"type": []int{LogTypeConsume, LogTypeRefund},
					},
				},
			},
		},
	}

	// 添加过滤条件
	must := quotaQuery["bool"].(map[string]interface{})["must"].([]map[string]interface{})

	if startTimestamp != 0 || endTimestamp != 0 {
		rangeQuery := map[string]interface{}{}
		if startTimestamp != 0 {
			rangeQuery["gte"] = time.Unix(startTimestamp, 0).Format(time.RFC3339)
		}
		if endTimestamp != 0 {
			rangeQuery["lte"] = time.Unix(endTimestamp, 0).Format(time.RFC3339)
		}
		must = append(must, map[string]interface{}{
			"range": map[string]interface{}{
				"created_at": rangeQuery,
			},
		})
	}

	// 添加其他过滤条件
	stringFilters := map[string]string{
		"username":    username,
		"token_name":  tokenName,
		"token_key":   tokenKey,
		"token_group": tokenGroup,
		"model_name":  modelName,
	}

	for field, value := range stringFilters {
		if value != "" {
			must = append(must, map[string]interface{}{
				"term": map[string]interface{}{
					field: value,
				},
			})
		}
	}

	if channel != 0 {
		must = append(must, map[string]interface{}{
			"term": map[string]interface{}{
				"channel_id": channel,
			},
		})
	}

	quotaQuery["bool"].(map[string]interface{})["must"] = must

	// 查询总配额
	quotaSearchRequest := map[string]interface{}{
		"query": quotaQuery,
		"aggs": map[string]interface{}{
			"total_quota": map[string]interface{}{
				"sum": map[string]interface{}{
					"field": "quota",
				},
			},
		},
		"size": 0,
	}

	var quotaResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", quotaSearchRequest, &quotaResult)
	if err != nil {
		return Stat{}, err
	}

	// 解析总配额结果
	if aggs, ok := quotaResult["aggregations"].(map[string]interface{}); ok {
		if totalQuota, ok := aggs["total_quota"].(map[string]interface{}); ok {
			if value, ok := totalQuota["value"].(float64); ok {
				stat.Quota = int(value)
			}
		}
	}

	// 获取当前时间，并计算一分钟之前的时间
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute)

	// 构建最近一分钟的查询 - 用于计算RPM和TPM
	rpmTpmQuery := map[string]interface{}{
		"bool": map[string]interface{}{
			"must": []map[string]interface{}{
				{
					"terms": map[string]interface{}{
						"type": []int{LogTypeConsume, LogTypeRefund},
					},
				},
				{
					"range": map[string]interface{}{
						"created_at": map[string]interface{}{
							"gte": oneMinuteAgo.Format(time.RFC3339),
							"lte": now.Format(time.RFC3339),
						},
					},
				},
			},
		},
	}

	// 添加相同的过滤条件到RPM/TPM查询
	rpmTpmMust := rpmTpmQuery["bool"].(map[string]interface{})["must"].([]map[string]interface{})
	for field, value := range stringFilters {
		if value != "" {
			rpmTpmMust = append(rpmTpmMust, map[string]interface{}{
				"term": map[string]interface{}{
					field: value,
				},
			})
		}
	}

	if channel != 0 {
		rpmTpmMust = append(rpmTpmMust, map[string]interface{}{
			"term": map[string]interface{}{
				"channel_id": channel,
			},
		})
	}

	rpmTpmQuery["bool"].(map[string]interface{})["must"] = rpmTpmMust

	// 查询最近一分钟的RPM和TPM
	rpmTpmSearchRequest := map[string]interface{}{
		"query": rpmTpmQuery,
		"aggs": map[string]interface{}{
			"total_requests": map[string]interface{}{
				"value_count": map[string]interface{}{
					"field": "id", // 使用业务ID字段而不是_id
				},
			},
			"total_tokens": map[string]interface{}{
				"sum": map[string]interface{}{
					"script": map[string]interface{}{
						"source": "doc['prompt_tokens'].value + doc['completion_tokens'].value",
					},
				},
			},
			"total_quota_minute": map[string]interface{}{
				"sum": map[string]interface{}{
					"field": "quota",
				},
			},
		},
		"size": 0,
	}

	var rpmTpmResult map[string]interface{}
	err = e.makeRequest("POST", "/"+e.indexName+"/_search", rpmTpmSearchRequest, &rpmTpmResult)
	if err != nil {
		return Stat{}, err
	}

	// 解析RPM和TPM结果
	if aggs, ok := rpmTpmResult["aggregations"].(map[string]interface{}); ok {
		if totalRequests, ok := aggs["total_requests"].(map[string]interface{}); ok {
			if value, ok := totalRequests["value"].(float64); ok {
				stat.Rpm = int(value)
			}
		}
		if totalTokens, ok := aggs["total_tokens"].(map[string]interface{}); ok {
			if value, ok := totalTokens["value"].(float64); ok {
				stat.Tpm = int(value)
			}
		}
		if totalQuotaMinute, ok := aggs["total_quota_minute"].(map[string]interface{}); ok {
			if value, ok := totalQuotaMinute["value"].(float64); ok {
				stat.Mpm = value / 500000
			}
		}
	}

	stat.IsRealtimeData = false // 标记为历史数据
	return stat, nil
}

// SumUsedQuotaByKey 根据key统计使用的配额
func (e *ElasticsearchLogStorage) SumUsedQuotaByKey(key string, startTimestamp int64, endTimestamp int64) (Stat, error) {
	return e.SumUsedQuota(0, startTimestamp, endTimestamp, "", "", "", key, "", 0, false)
}

// SumAllDailyUsageStatsByDimension 按维度统计每日使用情况
func (e *ElasticsearchLogStorage) SumAllDailyUsageStatsByDimension(userId int, timezone string, tokenName string, username string,
	channel int, channelName string, modelName string, startTimestamp int64, endTimestamp int64,
	dimension string, granularity string) ([]*DailyModelUsageStats, error) {

	// 构建基础查询条件
	mustQueries := []map[string]interface{}{}

	// 用户过滤
	if userId != 0 {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"user_id": userId,
			},
		})
	}

	// 字符串字段过滤
	stringFilters := map[string]string{
		"token_name":   tokenName,
		"username":     username,
		"channel_name": channelName,
		"model_name":   modelName,
	}

	for field, value := range stringFilters {
		if value != "" {
			mustQueries = append(mustQueries, map[string]interface{}{
				"term": map[string]interface{}{
					field: value,
				},
			})
		}
	}

	// 渠道过滤
	if channel != 0 {
		mustQueries = append(mustQueries, map[string]interface{}{
			"term": map[string]interface{}{
				"channel_id": channel,
			},
		})
	}

	// 时间范围过滤
	if startTimestamp != 0 || endTimestamp != 0 {
		rangeQuery := map[string]interface{}{}
		if startTimestamp != 0 {
			rangeQuery["gte"] = time.Unix(startTimestamp, 0).Format(time.RFC3339)
		}
		if endTimestamp != 0 {
			rangeQuery["lte"] = time.Unix(endTimestamp, 0).Format(time.RFC3339)
		}
		mustQueries = append(mustQueries, map[string]interface{}{
			"range": map[string]interface{}{
				"created_at": rangeQuery,
			},
		})
	}

	// 构建日期直方图聚合
	var interval string
	switch granularity {
	case "hour":
		interval = "1h"
	case "day":
		interval = "1d"
	case "week":
		interval = "1w"
	case "month":
		interval = "1M"
	default:
		interval = "1d"
	}

	// 构建聚合查询
	searchRequest := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"must": mustQueries,
			},
		},
		"aggs": map[string]interface{}{
			"daily_stats": map[string]interface{}{
				"date_histogram": map[string]interface{}{
					"field":    "created_at",
					"interval": interval,
					"format":   "yyyy-MM-dd",
				},
				"aggs": map[string]interface{}{
					"total_quota": map[string]interface{}{
						"sum": map[string]interface{}{
							"field": "quota",
						},
					},
					"cost_quota": map[string]interface{}{
						"sum": map[string]interface{}{
							"field": "cost_quota",
						},
					},
				},
			},
		},
		"size": 0,
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	// 解析聚合结果
	var stats []*DailyModelUsageStats
	if aggs, ok := searchResult["aggregations"].(map[string]interface{}); ok {
		if dailyStats, ok := aggs["daily_stats"].(map[string]interface{}); ok {
			if buckets, ok := dailyStats["buckets"].([]interface{}); ok {
				for _, bucket := range buckets {
					if bucketMap, ok := bucket.(map[string]interface{}); ok {
						stat := &DailyModelUsageStats{}

						// 解析日期
						if keyAsString, ok := bucketMap["key_as_string"].(string); ok {
							stat.Date = keyAsString
						}

						// 解析总配额
						if totalQuota, ok := bucketMap["total_quota"].(map[string]interface{}); ok {
							if value, ok := totalQuota["value"].(float64); ok {
								stat.SumQuota = int(value)
								stat.SumUsd = value / 500000
							}
						}

						// 解析成本配额
						if costQuota, ok := bucketMap["cost_quota"].(map[string]interface{}); ok {
							if value, ok := costQuota["value"].(float64); ok {
								stat.CostQuota = int(value)
								stat.CostUsd = value / 500000
							}
						}

						stats = append(stats, stat)
					}
				}
			}
		}
	}

	return stats, nil
}

// HealthCheck 健康检查
func (e *ElasticsearchLogStorage) HealthCheck() error {
	return e.makeRequest("GET", "/_cluster/health", nil, nil)
}

// Close 优雅关闭存储
func (e *ElasticsearchLogStorage) Close() error {
	logger.SysLog("Shutting down Elasticsearch storage...")

	// 打印最终指标
	metrics := e.metrics.GetMetrics()
	logger.SysLog(fmt.Sprintf("Final batch metrics - Total batches: %d, Total logs: %d, Failed batches: %d, Failed logs: %d, Avg batch size: %.2f, Backpressure hits: %d",
		metrics.TotalBatches, metrics.TotalLogs, metrics.FailedBatches, metrics.FailedLogs, metrics.AvgBatchSize, metrics.BackpressureHit))

	// 停止所有协程
	close(e.stopCh)

	// 等待所有协程结束
	e.wg.Wait()

	// 关闭通道
	if e.batchCh != nil {
		close(e.batchCh)
	}

	logger.SysLog("Elasticsearch storage shutdown completed")
	return nil
}

// GetBatchMetrics 获取批量写入指标
func (e *ElasticsearchLogStorage) GetBatchMetrics() BatchMetrics {
	return e.metrics.GetMetrics()
}

// GetStorageMetrics 获取存储监控指标
func (e *ElasticsearchLogStorage) GetStorageMetrics() StorageMetrics {
	// 更新索引统计信息
	e.updateIndexMetrics()
	return e.storageMetrics.GetStorageMetrics()
}

// updateIndexMetrics 更新索引指标
func (e *ElasticsearchLogStorage) updateIndexMetrics() {
	// 获取索引统计信息
	stats, err := e.getIndexStats()
	if err != nil {
		logger.SysError("Failed to get index stats: " + err.Error())
		return
	}

	e.storageMetrics.mu.Lock()
	defer e.storageMetrics.mu.Unlock()

	e.storageMetrics.TotalIndices = stats.TotalIndices
	e.storageMetrics.TotalDocuments = stats.TotalDocuments
	e.storageMetrics.IndexSize = stats.IndexSize
	e.storageMetrics.BatchMetrics = e.metrics.GetMetrics()
}

// IndexStats 索引统计信息
type IndexStats struct {
	TotalIndices   int   `json:"total_indices"`
	TotalDocuments int64 `json:"total_documents"`
	IndexSize      int64 `json:"index_size_bytes"`
}

// getIndexStats 获取索引统计信息
func (e *ElasticsearchLogStorage) getIndexStats() (*IndexStats, error) {
	// 获取所有相关索引的统计信息
	pattern := e.indexName + "*"

	var statsResult map[string]interface{}
	err := e.makeRequest("GET", "/"+pattern+"/_stats", nil, &statsResult)
	if err != nil {
		return nil, err
	}

	stats := &IndexStats{}

	// 解析统计结果
	if indices, ok := statsResult["indices"].(map[string]interface{}); ok {
		stats.TotalIndices = len(indices)

		for _, indexData := range indices {
			if indexInfo, ok := indexData.(map[string]interface{}); ok {
				// 获取文档数量
				if primaries, ok := indexInfo["primaries"].(map[string]interface{}); ok {
					if docs, ok := primaries["docs"].(map[string]interface{}); ok {
						if count, ok := docs["count"].(float64); ok {
							stats.TotalDocuments += int64(count)
						}
					}

					// 获取存储大小
					if store, ok := primaries["store"].(map[string]interface{}); ok {
						if sizeInBytes, ok := store["size_in_bytes"].(float64); ok {
							stats.IndexSize += int64(sizeInBytes)
						}
					}
				}
			}
		}
	}

	return stats, nil
}

// DiagnoseStorage 诊断存储状态
func (e *ElasticsearchLogStorage) DiagnoseStorage() map[string]interface{} {
	diagnosis := make(map[string]interface{})

	// 基本连接信息
	diagnosis["hosts"] = e.hosts
	diagnosis["index_name"] = e.indexName

	// 健康状态
	healthyHosts := e.getHealthyHosts()
	diagnosis["healthy_hosts"] = healthyHosts
	diagnosis["unhealthy_hosts"] = e.getUnhealthyHosts()
	diagnosis["health_ratio"] = float64(len(healthyHosts)) / float64(len(e.hosts))

	// 性能指标
	metrics := e.GetStorageMetrics()
	diagnosis["metrics"] = metrics

	// 缓存已移除，确保数据实时性
	diagnosis["cache_disabled"] = true

	// 批量写入状态
	diagnosis["batch_channel_size"] = len(e.batchCh)
	diagnosis["backpressure_size"] = len(e.backpressure)

	// 集群健康检查
	clusterHealth, err := e.getClusterHealth()
	if err != nil {
		diagnosis["cluster_health_error"] = err.Error()
	} else {
		diagnosis["cluster_health"] = clusterHealth
	}

	return diagnosis
}

// getUnhealthyHosts 获取不健康的主机列表
func (e *ElasticsearchLogStorage) getUnhealthyHosts() []string {
	e.healthMu.RLock()
	defer e.healthMu.RUnlock()

	var unhealthyHosts []string
	for host, isHealthy := range e.healthStatus {
		if !isHealthy {
			unhealthyHosts = append(unhealthyHosts, host)
		}
	}

	return unhealthyHosts
}

// getClusterHealth 获取集群健康状态
func (e *ElasticsearchLogStorage) getClusterHealth() (map[string]interface{}, error) {
	var healthResult map[string]interface{}
	err := e.makeRequest("GET", "/_cluster/health", nil, &healthResult)
	return healthResult, err
}

// 缓存功能已移除，确保数据查询的实时性

// IDGenerator 业务ID生成器
type IDGenerator struct {
	counter   int64
	tableName string // 表名，用于区分不同表的ID序列
	mu        sync.Mutex
}

// NewIDGenerator 创建新的业务ID生成器
func NewIDGenerator(tableName string, startID int64) *IDGenerator {
	return &IDGenerator{
		counter:   startID,
		tableName: tableName,
	}
}

// NextID 生成下一个业务ID
func (g *IDGenerator) NextID() int {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.counter++
	return int(g.counter)
}

// GetCurrentCounter 获取当前计数器值
func (g *IDGenerator) GetCurrentCounter() int64 {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.counter
}

// getMaxLogID 获取Log表中的最大ID
func (e *ElasticsearchLogStorage) getMaxLogID() (int64, error) {
	// 首先尝试使用max聚合（适用于integer类型的id字段）
	query := map[string]interface{}{
		"size": 0, // 不返回文档，只返回聚合结果
		"aggs": map[string]interface{}{
			"max_id": map[string]interface{}{
				"max": map[string]interface{}{
					"field": "id",
				},
			},
		},
	}

	var result map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", query, &result)
	if err != nil {
		// 如果是字段类型不支持聚合的错误，尝试使用脚本聚合
		if strings.Contains(err.Error(), "not supported for aggregation") ||
			strings.Contains(err.Error(), "keyword") {
			return e.getMaxLogIDWithScript()
		}

		// 如果索引不存在或查询失败，返回0
		if strings.Contains(err.Error(), "404") || strings.Contains(err.Error(), "index_not_found") {
			return 0, nil
		}
		return 0, err
	}

	// 解析聚合结果
	if aggs, ok := result["aggregations"].(map[string]interface{}); ok {
		if maxID, ok := aggs["max_id"].(map[string]interface{}); ok {
			if value, ok := maxID["value"]; ok {
				if value == nil {
					return 0, nil // 没有数据
				}
				if maxVal, ok := value.(float64); ok {
					return int64(maxVal), nil
				}
			}
		}
	}

	return 0, nil
}

// getMaxLogIDWithScript 使用脚本聚合获取最大ID（处理keyword类型的id字段）
func (e *ElasticsearchLogStorage) getMaxLogIDWithScript() (int64, error) {
	// 使用脚本聚合，将keyword类型的id转换为数值进行比较
	query := map[string]interface{}{
		"size": 0,
		"aggs": map[string]interface{}{
			"max_id": map[string]interface{}{
				"max": map[string]interface{}{
					"script": map[string]interface{}{
						"source": "Long.parseLong(doc['id'].value)",
						"lang":   "painless",
					},
				},
			},
		},
	}

	var result map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", query, &result)
	if err != nil {
		// 如果脚本聚合也失败，使用排序查询获取最大ID
		return e.getMaxLogIDWithSort()
	}

	// 解析聚合结果
	if aggs, ok := result["aggregations"].(map[string]interface{}); ok {
		if maxID, ok := aggs["max_id"].(map[string]interface{}); ok {
			if value, ok := maxID["value"]; ok {
				if value == nil {
					return 0, nil
				}
				if maxVal, ok := value.(float64); ok {
					return int64(maxVal), nil
				}
			}
		}
	}

	return 0, nil
}

// getMaxLogIDWithSort 使用排序查询获取最大ID（最后的回退方案）
func (e *ElasticsearchLogStorage) getMaxLogIDWithSort() (int64, error) {
	// 使用排序查询获取ID最大的文档
	query := map[string]interface{}{
		"size": 1,
		"sort": []map[string]interface{}{
			{
				"_script": map[string]interface{}{
					"type": "number",
					"script": map[string]interface{}{
						"source": "Long.parseLong(doc['id'].value)",
						"lang":   "painless",
					},
					"order": "desc",
				},
			},
		},
		"_source": []string{"id"},
	}

	var result map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", query, &result)
	if err != nil {
		return 0, err
	}

	// 解析查询结果
	if hits, ok := result["hits"].(map[string]interface{}); ok {
		if hitsArray, ok := hits["hits"].([]interface{}); ok && len(hitsArray) > 0 {
			if hit, ok := hitsArray[0].(map[string]interface{}); ok {
				if source, ok := hit["_source"].(map[string]interface{}); ok {
					if idValue, ok := source["id"]; ok {
						// 处理不同类型的id值
						switch v := idValue.(type) {
						case float64:
							return int64(v), nil
						case string:
							if id, err := strconv.ParseInt(v, 10, 64); err == nil {
								return id, nil
							}
						case int:
							return int64(v), nil
						case int64:
							return v, nil
						}
					}
				}
			}
		}
	}

	return 0, nil
}

// getMaxLogExtendID 获取LogExtend表中的最大ID
func (e *ElasticsearchLogStorage) getMaxLogExtendID() (int64, error) {
	// 获取所有LogExtend索引
	indices, err := e.getLogExtendIndices()
	if err != nil || len(indices) == 0 {
		return 0, nil
	}

	// 构建聚合查询，跨所有LogExtend索引查询最大ID
	indexPattern := strings.Join(indices, ",")
	query := map[string]interface{}{
		"size": 0,
		"aggs": map[string]interface{}{
			"max_id": map[string]interface{}{
				"max": map[string]interface{}{
					"field": "id",
				},
			},
		},
	}

	var result map[string]interface{}
	err = e.makeRequest("POST", "/"+indexPattern+"/_search", query, &result)
	if err != nil {
		// 如果是字段类型不支持聚合的错误，尝试使用脚本聚合
		if strings.Contains(err.Error(), "not supported for aggregation") ||
			strings.Contains(err.Error(), "keyword") {
			return e.getMaxLogExtendIDWithScript(indexPattern)
		}

		if strings.Contains(err.Error(), "404") || strings.Contains(err.Error(), "index_not_found") {
			return 0, nil
		}
		return 0, err
	}

	// 解析聚合结果
	if aggs, ok := result["aggregations"].(map[string]interface{}); ok {
		if maxID, ok := aggs["max_id"].(map[string]interface{}); ok {
			if value, ok := maxID["value"]; ok {
				if value == nil {
					return 0, nil
				}
				if maxVal, ok := value.(float64); ok {
					return int64(maxVal), nil
				}
			}
		}
	}

	return 0, nil
}

// getMaxLogExtendIDWithScript 使用脚本聚合获取LogExtend最大ID
func (e *ElasticsearchLogStorage) getMaxLogExtendIDWithScript(indexPattern string) (int64, error) {
	query := map[string]interface{}{
		"size": 0,
		"aggs": map[string]interface{}{
			"max_id": map[string]interface{}{
				"max": map[string]interface{}{
					"script": map[string]interface{}{
						"source": "Long.parseLong(doc['id'].value)",
						"lang":   "painless",
					},
				},
			},
		},
	}

	var result map[string]interface{}
	err := e.makeRequest("POST", "/"+indexPattern+"/_search", query, &result)
	if err != nil {
		// 如果脚本聚合也失败，使用排序查询
		return e.getMaxLogExtendIDWithSort(indexPattern)
	}

	// 解析聚合结果
	if aggs, ok := result["aggregations"].(map[string]interface{}); ok {
		if maxID, ok := aggs["max_id"].(map[string]interface{}); ok {
			if value, ok := maxID["value"]; ok {
				if value == nil {
					return 0, nil
				}
				if maxVal, ok := value.(float64); ok {
					return int64(maxVal), nil
				}
			}
		}
	}

	return 0, nil
}

// getMaxLogExtendIDWithSort 使用排序查询获取LogExtend最大ID
func (e *ElasticsearchLogStorage) getMaxLogExtendIDWithSort(indexPattern string) (int64, error) {
	query := map[string]interface{}{
		"size": 1,
		"sort": []map[string]interface{}{
			{
				"_script": map[string]interface{}{
					"type": "number",
					"script": map[string]interface{}{
						"source": "Long.parseLong(doc['id'].value)",
						"lang":   "painless",
					},
					"order": "desc",
				},
			},
		},
		"_source": []string{"id"},
	}

	var result map[string]interface{}
	err := e.makeRequest("POST", "/"+indexPattern+"/_search", query, &result)
	if err != nil {
		return 0, err
	}

	// 解析查询结果
	if hits, ok := result["hits"].(map[string]interface{}); ok {
		if hitsArray, ok := hits["hits"].([]interface{}); ok && len(hitsArray) > 0 {
			if hit, ok := hitsArray[0].(map[string]interface{}); ok {
				if source, ok := hit["_source"].(map[string]interface{}); ok {
					if idValue, ok := source["id"]; ok {
						// 处理不同类型的id值
						switch v := idValue.(type) {
						case float64:
							return int64(v), nil
						case string:
							if id, err := strconv.ParseInt(v, 10, 64); err == nil {
								return id, nil
							}
						case int:
							return int64(v), nil
						case int64:
							return v, nil
						}
					}
				}
			}
		}
	}

	return 0, nil
}

// initializeIDGenerators 初始化ID生成器
func (e *ElasticsearchLogStorage) initializeIDGenerators() error {
	var logIDErr, logExtendIDErr error
	var maxLogID, maxLogExtendID int64

	// 获取Log表的最大ID
	maxLogID, logIDErr = e.getMaxLogID()
	if logIDErr != nil {
		logger.SysError("Failed to get max log ID: " + logIDErr.Error())
		maxLogID = 0 // 如果获取失败，从0开始而不是返回错误
	}

	// 获取LogExtend表的最大ID
	maxLogExtendID, logExtendIDErr = e.getMaxLogExtendID()
	if logExtendIDErr != nil {
		logger.SysError("Failed to get max log extend ID: " + logExtendIDErr.Error())
		maxLogExtendID = 0 // 如果获取失败，从0开始而不是返回错误
	}

	// 初始化ID生成器，起始值为最大ID
	e.logIDGenerator = NewIDGenerator("logs", maxLogID)
	e.logExtendIDGenerator = NewIDGenerator("log_extends", maxLogExtendID)

	logger.SysLog(fmt.Sprintf("Initialized ID generators - Log: %d, LogExtend: %d", maxLogID, maxLogExtendID))

	// 只有在两个ID都获取失败且是严重错误时才返回错误
	if logIDErr != nil && logExtendIDErr != nil {
		// 检查是否是索引不存在的错误（这是正常的，第一次启动时）
		if strings.Contains(logIDErr.Error(), "404") || strings.Contains(logIDErr.Error(), "index_not_found") ||
			strings.Contains(logExtendIDErr.Error(), "404") || strings.Contains(logExtendIDErr.Error(), "index_not_found") {
			return nil // 索引不存在是正常的，不返回错误
		}
		return fmt.Errorf("failed to initialize both ID generators: log=%v, logExtend=%v", logIDErr, logExtendIDErr)
	}

	return nil
}

// initializeIDGeneratorsWithFallback 使用回退逻辑初始化ID生成器
func (e *ElasticsearchLogStorage) initializeIDGeneratorsWithFallback() {
	var maxLogID, maxLogExtendID int64

	// 尝试获取Log表的最大ID
	if id, err := e.getMaxLogID(); err != nil {
		logger.SysError("Failed to get max log ID in fallback: " + err.Error())
		maxLogID = 0 // 如果获取失败，从0开始
	} else {
		maxLogID = id
	}

	// 尝试获取LogExtend表的最大ID
	if id, err := e.getMaxLogExtendID(); err != nil {
		logger.SysError("Failed to get max log extend ID in fallback: " + err.Error())
		maxLogExtendID = 0 // 如果获取失败，从0开始
	} else {
		maxLogExtendID = id
	}

	// 初始化ID生成器，即使获取失败也使用0作为起始值而不是时间戳
	e.logIDGenerator = NewIDGenerator("logs", maxLogID)
	e.logExtendIDGenerator = NewIDGenerator("log_extends", maxLogExtendID)

	logger.SysLog(fmt.Sprintf("Initialized ID generators with fallback - Log: %d, LogExtend: %d", maxLogID, maxLogExtendID))
}

// buildOptimizedQuery 构建优化的Elasticsearch查询
func (e *ElasticsearchLogStorage) buildOptimizedQuery(logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64, requestDurationMax *float64,
	responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64, excludeModels []string,
	errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) map[string]interface{} {

	must := []map[string]interface{}{}
	filter := []map[string]interface{}{}

	// 使用filter context而不是query context来提高性能（不计算相关性分数）

	// 日志类型过滤
	if len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		filter = append(filter, map[string]interface{}{
			"terms": map[string]interface{}{
				"type": logType,
			},
		})
	}

	// 时间范围过滤（使用filter context）
	if startTimestamp != 0 || endTimestamp != 0 {
		rangeQuery := map[string]interface{}{}
		if startTimestamp != 0 {
			rangeQuery["gte"] = time.Unix(startTimestamp, 0).Format(time.RFC3339)
		}
		if endTimestamp != 0 {
			rangeQuery["lte"] = time.Unix(endTimestamp, 0).Format(time.RFC3339)
		}
		filter = append(filter, map[string]interface{}{
			"range": map[string]interface{}{
				"created_at": rangeQuery,
			},
		})
	}

	// 精确匹配字段使用filter context
	if modelName != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"model_name": modelName,
			},
		})
	}

	if username != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"username": username,
			},
		})
	}

	if tokenName != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"token_name": tokenName,
			},
		})
	}

	if tokenKey != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"token_key": tokenKey,
			},
		})
	}

	if tokenGroup != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"token_group": tokenGroup,
			},
		})
	}

	if channelName != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"channel_name": channelName,
			},
		})
	}

	if channel != 0 {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"channel_id": channel,
			},
		})
	}

	if isStream != "" {
		streamValue := isStream == "true"
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"is_stream": streamValue,
			},
		})
	}

	if requestId != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"request_id": requestId,
			},
		})
	}

	if ip != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"ip": ip,
			},
		})
	}

	if errorCode != "" {
		filter = append(filter, map[string]interface{}{
			"term": map[string]interface{}{
				"error_code": errorCode,
			},
		})
	}

	// 数值范围查询
	addRangeFilter := func(field string, min, max interface{}) {
		if min != nil || max != nil {
			rangeQuery := map[string]interface{}{}
			if min != nil {
				rangeQuery["gte"] = min
			}
			if max != nil {
				rangeQuery["lte"] = max
			}
			filter = append(filter, map[string]interface{}{
				"range": map[string]interface{}{
					field: rangeQuery,
				},
			})
		}
	}

	addRangeFilter("prompt_tokens", promptTokensMin, promptTokensMax)
	addRangeFilter("completion_tokens", completionTokensMin, completionTokensMax)
	addRangeFilter("total_duration", totalDurationMin, totalDurationMax)
	addRangeFilter("request_duration", requestDurationMin, requestDurationMax)
	addRangeFilter("response_first_byte_duration", responseFirstByteDurationMin, responseFirstByteDurationMax)
	addRangeFilter("quota", quotaMin, quotaMax)

	// 排除模型
	if len(excludeModels) > 0 {
		filter = append(filter, map[string]interface{}{
			"bool": map[string]interface{}{
				"must_not": []map[string]interface{}{
					{
						"terms": map[string]interface{}{
							"model_name": excludeModels,
						},
					},
				},
			},
		})
	}

	// 排除错误码
	if len(excludeErrorCodes) > 0 {
		filter = append(filter, map[string]interface{}{
			"bool": map[string]interface{}{
				"must_not": []map[string]interface{}{
					{
						"terms": map[string]interface{}{
							"error_code": excludeErrorCodes,
						},
					},
				},
			},
		})
	}

	// 构建最终查询
	query := map[string]interface{}{
		"bool": map[string]interface{}{},
	}

	if len(must) > 0 {
		query["bool"].(map[string]interface{})["must"] = must
	}

	if len(filter) > 0 {
		query["bool"].(map[string]interface{})["filter"] = filter
	}

	return query
}

// RecordLogExtend 记录LogExtend（改进版本）
func (e *ElasticsearchLogStorage) RecordLogExtend(ctx context.Context, logExtend *LogExtend) error {
	// 如果业务ID为0，生成新的业务ID
	if logExtend.Id == 0 {
		logExtend.Id = e.logExtendIDGenerator.NextID()
	}

	// 确保LogExtend索引存在
	indexName := e.getLogExtendIndexName(time.Unix(logExtend.CreatedAt, 0))
	if err := e.ensureLogExtendIndex(indexName); err != nil {
		return fmt.Errorf("failed to ensure LogExtend index: %v", err)
	}

	// 构建文档
	doc := map[string]interface{}{
		"id":                logExtend.Id,
		"log_id":            logExtend.LogId,
		"created_at":        time.Unix(logExtend.CreatedAt, 0).Format(time.RFC3339),
		"prompt":            logExtend.Prompt,
		"completion":        logExtend.Completion,
		"completion_id":     logExtend.CompletionId,
		"upstream_response": logExtend.UpstreamResponse,
		"full_response":     logExtend.FullResponse,
		"request_path":      logExtend.RequestPath,
	}

	// 让ES自动生成文档_id，但业务ID存储在文档中
	return e.makeRequest("POST", "/"+indexName+"/_doc", doc, nil)
}

// RecordLogExtendBatch 批量记录LogExtend
func (e *ElasticsearchLogStorage) RecordLogExtendBatch(ctx context.Context, logExtends []*LogExtend) error {
	if len(logExtends) == 0 {
		return nil
	}

	// 构建批量请求
	var bulkBody strings.Builder

	for _, logExtend := range logExtends {
		// 如果业务ID为0，生成新的业务ID
		if logExtend.Id == 0 {
			logExtend.Id = e.logExtendIDGenerator.NextID()
		}

		// 构建索引名称
		indexName := e.getLogExtendIndexName(time.Unix(logExtend.CreatedAt, 0))

		// 构建action行，让ES自动生成文档_id
		action := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": indexName,
				// 不指定_id，让ES自动生成
			},
		}
		actionBytes, _ := json.Marshal(action)
		bulkBody.Write(actionBytes)
		bulkBody.WriteString("\n")

		// 构建文档行
		doc := map[string]interface{}{
			"id":                logExtend.Id,
			"log_id":            logExtend.LogId,
			"created_at":        time.Unix(logExtend.CreatedAt, 0).Format(time.RFC3339),
			"prompt":            logExtend.Prompt,
			"completion":        logExtend.Completion,
			"completion_id":     logExtend.CompletionId,
			"upstream_response": logExtend.UpstreamResponse,
			"full_response":     logExtend.FullResponse,
			"request_path":      logExtend.RequestPath,
		}
		docBytes, _ := json.Marshal(doc)
		bulkBody.Write(docBytes)
		bulkBody.WriteString("\n")
	}

	// 发送批量请求
	url := fmt.Sprintf("%s/_bulk", e.hosts[0])
	req, err := http.NewRequestWithContext(ctx, "POST", url, strings.NewReader(bulkBody.String()))
	if err != nil {
		return fmt.Errorf("failed to create bulk request: %v", err)
	}

	req.Header.Set("Content-Type", "application/x-ndjson")
	if e.username != "" {
		req.SetBasicAuth(e.username, e.password)
	}

	resp, err := e.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send bulk request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("elasticsearch bulk error: %s", string(body))
	}

	return nil
}

// getLogExtendIndexName 获取LogExtend索引名称
func (e *ElasticsearchLogStorage) getLogExtendIndexName(t time.Time) string {
	// 如果配置了自定义扩展索引名，使用自定义名称
	if config.ElasticsearchExtendIndex != "" {
		// 检查是否需要添加日期后缀
		if config.ElasticsearchExtendIndexDateFormat == "none" {
			return config.ElasticsearchExtendIndex
		} else {
			return fmt.Sprintf("%s-%s", config.ElasticsearchExtendIndex, t.Format(config.ElasticsearchExtendIndexDateFormat))
		}
	}

	// 使用默认格式：主索引名-extends-日期
	if config.ElasticsearchExtendIndexDateFormat == "none" {
		return fmt.Sprintf("%s-extends", e.indexName)
	} else {
		return fmt.Sprintf("%s-extends-%s", e.indexName, t.Format(config.ElasticsearchExtendIndexDateFormat))
	}
}

// ensureLogExtendIndex 确保LogExtend索引存在
func (e *ElasticsearchLogStorage) ensureLogExtendIndex(indexName string) error {
	// 检查索引是否存在
	exists, err := e.indexExists(indexName)
	if err != nil {
		return err
	}

	if exists {
		return nil
	}

	// 创建LogExtend索引
	indexMapping := e.buildLogExtendIndexMapping()
	err = e.makeRequest("PUT", "/"+indexName, indexMapping, nil)
	if err != nil {
		return fmt.Errorf("failed to create LogExtend index %s: %v", indexName, err)
	}

	logger.SysLog(fmt.Sprintf("Created LogExtend index: %s", indexName))
	return nil
}

// buildLogExtendIndexMapping 构建LogExtend索引映射
func (e *ElasticsearchLogStorage) buildLogExtendIndexMapping() map[string]interface{} {
	return map[string]interface{}{
		"settings": map[string]interface{}{
			"number_of_shards":   1, // LogExtend数据量相对较小
			"number_of_replicas": config.ElasticsearchReplicas,
			"refresh_interval":   "30s",              // 较长的刷新间隔
			"codec":              "best_compression", // 启用压缩
		},
		"mappings": map[string]interface{}{
			"properties": map[string]interface{}{
				"id": map[string]interface{}{
					"type":  "integer",
					"index": true,
				},
				"log_id": map[string]interface{}{
					"type":  "integer",
					"index": true,
				},
				"created_at": map[string]interface{}{
					"type":   "date",
					"format": "strict_date_optional_time||epoch_second",
					"index":  true,
				},
				"prompt": map[string]interface{}{
					"type":     "text",
					"analyzer": "log_analyzer",
					"index":    false, // 不索引，只存储
				},
				"completion": map[string]interface{}{
					"type":     "text",
					"analyzer": "log_analyzer",
					"index":    false, // 不索引，只存储
				},
				"completion_id": map[string]interface{}{
					"type":  "keyword",
					"index": true,
				},
				"upstream_response": map[string]interface{}{
					"type":  "text",
					"index": false, // 不索引，只存储
				},
				"full_response": map[string]interface{}{
					"type":  "text",
					"index": false, // 不索引，只存储
				},
				"request_path": map[string]interface{}{
					"type":  "keyword",
					"index": true,
				},
			},
			"dynamic": "strict", // 严格模式
		},
	}
}

// GetLogExtendByLogId 根据LogId获取LogExtend
func (e *ElasticsearchLogStorage) GetLogExtendByLogId(logId int) (*LogExtend, error) {
	// 构建查询
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"term": map[string]interface{}{
				"log_id": logId,
			},
		},
		"sort": []map[string]interface{}{
			{"created_at": map[string]string{"order": "desc"}},
		},
		"size": 1,
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal query: %v", err)
	}

	// 搜索所有LogExtend索引
	indexPattern := e.getLogExtendIndexPattern()
	url := fmt.Sprintf("%s/%s/_search", e.hosts[0], indexPattern)
	req, err := http.NewRequest("POST", url, bytes.NewReader(queryBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if e.username != "" {
		req.SetBasicAuth(e.username, e.password)
	}

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("elasticsearch error: %s", string(body))
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	hits, ok := result["hits"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format")
	}

	hitsArray, ok := hits["hits"].([]interface{})
	if !ok || len(hitsArray) == 0 {
		return nil, fmt.Errorf("log extend not found")
	}

	// 解析第一个结果
	hit := hitsArray[0].(map[string]interface{})
	source := hit["_source"].(map[string]interface{})

	logExtend := &LogExtend{}
	logExtend.Id = int(source["id"].(float64))
	logExtend.LogId = int(source["log_id"].(float64))

	if createdAtStr, ok := source["created_at"].(string); ok {
		if createdAt, err := time.Parse(time.RFC3339, createdAtStr); err == nil {
			logExtend.CreatedAt = createdAt.Unix()
		}
	}

	if prompt, ok := source["prompt"].(string); ok {
		logExtend.Prompt = prompt
	}
	if completion, ok := source["completion"].(string); ok {
		logExtend.Completion = completion
	}
	if completionId, ok := source["completion_id"].(string); ok {
		logExtend.CompletionId = completionId
	}
	if upstreamResponse, ok := source["upstream_response"].(string); ok {
		logExtend.UpstreamResponse = upstreamResponse
	}
	if fullResponse, ok := source["full_response"].(string); ok {
		logExtend.FullResponse = fullResponse
	}
	if requestPath, ok := source["request_path"].(string); ok {
		logExtend.RequestPath = requestPath
	}

	return logExtend, nil
}

// DeleteLogExtendByLogId 根据LogId删除LogExtend
func (e *ElasticsearchLogStorage) DeleteLogExtendByLogId(logId int) error {
	// 构建删除查询
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"term": map[string]interface{}{
				"log_id": logId,
			},
		},
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return fmt.Errorf("failed to marshal query: %v", err)
	}

	// 删除所有LogExtend索引中的匹配记录
	url := fmt.Sprintf("%s/%s-extends-*/_delete_by_query", e.hosts[0], e.indexName)
	req, err := http.NewRequest("POST", url, bytes.NewReader(queryBytes))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if e.username != "" {
		req.SetBasicAuth(e.username, e.password)
	}

	resp, err := e.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("elasticsearch error: %s", string(body))
	}

	return nil
}

// DeleteLogExtendByTimestamp 根据时间戳删除LogExtend
func (e *ElasticsearchLogStorage) DeleteLogExtendByTimestamp(targetTimestamp int64) (int64, error) {
	// 构建删除查询
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"range": map[string]interface{}{
				"created_at": map[string]interface{}{
					"lt": time.Unix(targetTimestamp, 0).Format(time.RFC3339),
				},
			},
		},
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal query: %v", err)
	}

	// 先统计要删除的记录数
	countUrl := fmt.Sprintf("%s/%s-extends-*/_count", e.hosts[0], e.indexName)
	countReq, err := http.NewRequest("POST", countUrl, bytes.NewReader(queryBytes))
	if err != nil {
		return 0, fmt.Errorf("failed to create count request: %v", err)
	}

	countReq.Header.Set("Content-Type", "application/json")
	if e.username != "" {
		countReq.SetBasicAuth(e.username, e.password)
	}

	countResp, err := e.client.Do(countReq)
	if err != nil {
		return 0, fmt.Errorf("failed to send count request: %v", err)
	}
	defer countResp.Body.Close()

	var countResult map[string]interface{}
	if err := json.NewDecoder(countResp.Body).Decode(&countResult); err != nil {
		return 0, fmt.Errorf("failed to decode count response: %v", err)
	}

	count := int64(countResult["count"].(float64))

	// 执行删除
	deleteUrl := fmt.Sprintf("%s/%s-extends-*/_delete_by_query", e.hosts[0], e.indexName)
	deleteReq, err := http.NewRequest("POST", deleteUrl, bytes.NewReader(queryBytes))
	if err != nil {
		return 0, fmt.Errorf("failed to create delete request: %v", err)
	}

	deleteReq.Header.Set("Content-Type", "application/json")
	if e.username != "" {
		deleteReq.SetBasicAuth(e.username, e.password)
	}

	deleteResp, err := e.client.Do(deleteReq)
	if err != nil {
		return 0, fmt.Errorf("failed to send delete request: %v", err)
	}
	defer deleteResp.Body.Close()

	if deleteResp.StatusCode >= 400 {
		body, _ := io.ReadAll(deleteResp.Body)
		return 0, fmt.Errorf("elasticsearch delete error: %s", string(body))
	}

	return count, nil
}

// DeleteInvalidLogExtend 删除无效的LogExtend记录（完整实现）
func (e *ElasticsearchLogStorage) DeleteInvalidLogExtend() error {
	logger.SysLog("Starting cleanup of invalid LogExtend records...")

	totalDeleted := int64(0)
	batchSize := 1000

	// 获取所有LogExtend索引
	logExtendIndices, err := e.getLogExtendIndices()
	if err != nil {
		return fmt.Errorf("failed to get LogExtend indices: %v", err)
	}

	for _, indexName := range logExtendIndices {
		logger.SysLog(fmt.Sprintf("Checking LogExtend index: %s", indexName))

		// 分批处理每个索引
		from := 0
		for {
			// 获取一批LogExtend记录
			logExtends, err := e.getLogExtendBatch(indexName, from, batchSize)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to get LogExtend batch from %s: %v", indexName, err))
				break
			}

			if len(logExtends) == 0 {
				break // 没有更多记录
			}

			// 检查这批记录的有效性
			invalidIds, err := e.checkLogExtendValidity(logExtends)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to check LogExtend validity: %v", err))
				from += batchSize
				continue
			}

			// 删除无效记录
			if len(invalidIds) > 0 {
				deleted, err := e.deleteLogExtendByIds(indexName, invalidIds)
				if err != nil {
					logger.SysError(fmt.Sprintf("Failed to delete invalid LogExtend records: %v", err))
				} else {
					totalDeleted += deleted
					logger.SysLog(fmt.Sprintf("Deleted %d invalid LogExtend records from %s", deleted, indexName))
				}
			}

			from += batchSize
		}
	}

	logger.SysLog(fmt.Sprintf("Cleanup completed. Total deleted: %d invalid LogExtend records", totalDeleted))
	return nil
}

// getLogExtendIndices 获取所有LogExtend索引
func (e *ElasticsearchLogStorage) getLogExtendIndices() ([]string, error) {
	var patterns []string

	// 根据配置构建搜索模式
	if config.ElasticsearchExtendIndex != "" {
		// 使用自定义扩展索引名
		if config.ElasticsearchExtendIndexDateFormat == "none" {
			patterns = append(patterns, config.ElasticsearchExtendIndex)
		} else {
			patterns = append(patterns, config.ElasticsearchExtendIndex+"-*")
		}
	} else {
		// 使用默认格式
		if config.ElasticsearchExtendIndexDateFormat == "none" {
			patterns = append(patterns, e.indexName+"-extends")
		} else {
			patterns = append(patterns, e.indexName+"-extends-*")
		}
	}

	var allIndices []string

	// 对每个模式进行查询
	for _, pattern := range patterns {
		var result interface{}
		err := e.makeRequest("GET", "/_cat/indices/"+pattern+"?format=json", nil, &result)
		if err != nil {
			// 如果没有找到索引，继续下一个模式
			if strings.Contains(err.Error(), "404") {
				continue
			}
			return nil, err
		}

		// 解析索引列表
		if resultArray, ok := result.([]interface{}); ok {
			for _, item := range resultArray {
				if indexInfo, ok := item.(map[string]interface{}); ok {
					if indexName, ok := indexInfo["index"].(string); ok {
						allIndices = append(allIndices, indexName)
					}
				}
			}
		}
	}

	return allIndices, nil
}

// getLogExtendIndexPattern 获取LogExtend索引搜索模式
func (e *ElasticsearchLogStorage) getLogExtendIndexPattern() string {
	if config.ElasticsearchExtendIndex != "" {
		// 使用自定义扩展索引名
		if config.ElasticsearchExtendIndexDateFormat == "none" {
			return config.ElasticsearchExtendIndex
		} else {
			return config.ElasticsearchExtendIndex + "-*"
		}
	} else {
		// 使用默认格式
		if config.ElasticsearchExtendIndexDateFormat == "none" {
			return e.indexName + "-extends"
		} else {
			return e.indexName + "-extends-*"
		}
	}
}

// getLogExtendBatch 获取一批LogExtend记录
func (e *ElasticsearchLogStorage) getLogExtendBatch(indexName string, from, size int) ([]*LogExtend, error) {
	searchRequest := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
		"from":    from,
		"size":    size,
		"_source": []string{"id", "log_id", "created_at"}, // 只获取必要字段
		"sort": []map[string]interface{}{
			{"created_at": map[string]string{"order": "asc"}},
		},
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	// 解析结果
	hits, ok := searchResult["hits"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid search result format")
	}

	hitsList, ok := hits["hits"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid hits format")
	}

	var logExtends []*LogExtend
	for _, hit := range hitsList {
		hitMap, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}

		source, ok := hitMap["_source"].(map[string]interface{})
		if !ok {
			continue
		}

		logExtend := &LogExtend{}
		if id, ok := source["id"].(float64); ok {
			logExtend.Id = int(id)
		}
		if logId, ok := source["log_id"].(float64); ok {
			logExtend.LogId = int(logId)
		}
		if createdAtStr, ok := source["created_at"].(string); ok {
			if createdAt, err := time.Parse(time.RFC3339, createdAtStr); err == nil {
				logExtend.CreatedAt = createdAt.Unix()
			}
		}

		logExtends = append(logExtends, logExtend)
	}

	return logExtends, nil
}

// checkLogExtendValidity 检查LogExtend记录的有效性
func (e *ElasticsearchLogStorage) checkLogExtendValidity(logExtends []*LogExtend) ([]int, error) {
	if len(logExtends) == 0 {
		return []int{}, nil
	}

	// 提取所有log_id
	logIds := make([]int, len(logExtends))
	logIdToExtendId := make(map[int]int)

	for i, logExtend := range logExtends {
		logIds[i] = logExtend.LogId
		logIdToExtendId[logExtend.LogId] = logExtend.Id
	}

	// 批量检查这些log_id是否存在于主日志索引中
	existingLogIds, err := e.checkLogIdsExist(logIds)
	if err != nil {
		return nil, fmt.Errorf("failed to check log IDs existence: %v", err)
	}

	// 找出不存在的log_id对应的LogExtend ID
	existingLogIdSet := make(map[int]bool)
	for _, logId := range existingLogIds {
		existingLogIdSet[logId] = true
	}

	var invalidIds []int
	for _, logExtend := range logExtends {
		if !existingLogIdSet[logExtend.LogId] {
			invalidIds = append(invalidIds, logExtend.Id)
		}
	}

	return invalidIds, nil
}

// checkLogIdsExist 检查log ID是否存在于主日志索引中
func (e *ElasticsearchLogStorage) checkLogIdsExist(logIds []int) ([]int, error) {
	if len(logIds) == 0 {
		return []int{}, nil
	}

	// 构建查询，查找存在的log ID
	searchRequest := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"id": logIds,
			},
		},
		"_source": []string{"id"}, // 只返回ID字段
		"size":    len(logIds),
	}

	var searchResult map[string]interface{}
	err := e.makeRequest("POST", "/"+e.indexName+"/_search", searchRequest, &searchResult)
	if err != nil {
		return nil, err
	}

	// 解析结果
	hits, ok := searchResult["hits"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid search result format")
	}

	hitsList, ok := hits["hits"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid hits format")
	}

	var existingIds []int
	for _, hit := range hitsList {
		hitMap, ok := hit.(map[string]interface{})
		if !ok {
			continue
		}

		source, ok := hitMap["_source"].(map[string]interface{})
		if !ok {
			continue
		}

		if idStr, ok := source["id"].(string); ok {
			if id, err := strconv.Atoi(idStr); err == nil {
				existingIds = append(existingIds, id)
			}
		}
	}

	return existingIds, nil
}

// deleteLogExtendByIds 根据ID列表删除LogExtend记录
func (e *ElasticsearchLogStorage) deleteLogExtendByIds(indexName string, ids []int) (int64, error) {
	if len(ids) == 0 {
		return 0, nil
	}

	// 构建删除查询
	deleteQuery := map[string]interface{}{
		"query": map[string]interface{}{
			"terms": map[string]interface{}{
				"id": ids,
			},
		},
	}

	var deleteResult map[string]interface{}
	err := e.makeRequest("POST", "/"+indexName+"/_delete_by_query", deleteQuery, &deleteResult)
	if err != nil {
		return 0, err
	}

	// 解析删除结果
	if deleted, ok := deleteResult["deleted"].(float64); ok {
		return int64(deleted), nil
	}

	return 0, fmt.Errorf("failed to parse delete result")
}

// GetStorageType 获取存储类型
func (e *ElasticsearchLogStorage) GetStorageType() string {
	return "elasticsearch"
}

// RecordRefundLogByDetailIfZeroQuota 记录退款日志（如果配额为零）
func (e *ElasticsearchLogStorage) RecordRefundLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string,
	quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string) (*Log, error) {

	if ctx == nil {
		ctx = context.Background()
	}

	if requestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, requestId)
	}

	logger.Info(ctx, fmt.Sprintf("record Refund log: userId=%d, channelId=%d, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d,totalDuration=%d, content=%s", userId, channelId, promptTokens, completionTokens, modelName, tokenName, quota, totalDuration, content))

	if !config.LogConsumeEnabled {
		return nil, nil
	}

	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}

	log := &Log{
		UserId:                    userId,
		Username:                  CacheGetUsernameById(userId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeRefund,
		Content:                   content,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		ChannelName:               channelName,
		ModelName:                 modelName,
		Quota:                     quota,
		ChannelId:                 channelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
	}

	// 使用Elasticsearch的RecordLog方法
	err := e.RecordLog(ctx, log)
	if err != nil {
		logger.Error(ctx, "<RecordRefundLogByDetailIfZeroQuota> failed to record log: "+err.Error())
		return log, err
	}

	return log, nil
}

// RecordSysLogToDBAndFile 记录系统日志到数据库和文件
func (e *ElasticsearchLogStorage) RecordSysLogToDBAndFile(ctx context.Context, requestId string, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) error {
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := getErrorCodeFromContext(ctx)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return nil
		}

		// 截断content和prompt
		content = TruncateOptimized(content, config.MaxPromptLogLength, modelName)
		if prompt != "" {
			prompt = TruncateOptimized(prompt, config.MaxPromptLogLength, modelName)
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}

	if requestId == "" {
		requestId1, ok := ctx.Value(helper.RequestIdKey).(string)
		if ok {
			requestId = requestId1
		}
	}

	log := &Log{
		RequestId:   requestId,
		UserId:      userId,
		Username:    CacheGetUsernameById(userId),
		CreatedAt:   helper.GetTimestamp(),
		Type:        logType,
		Content:     content,
		TokenName:   tokenName,
		ChannelName: channelName,
		ModelName:   modelName,
		ChannelId:   channelId,
		ErrorCode:   getErrorCodeFromContext(ctx), // 从context中获取错误码
	}

	// 使用Elasticsearch的RecordLog方法
	err := e.RecordLog(ctx, log)
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record log: "+err.Error())
		return err
	}

	if prompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, config.MaxPromptLogLength, modelName)
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = e.RecordLogExtend(ctx, logExtend)
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// RecordSysLogToDBAndFileByGinContext 记录系统日志到数据库和文件（通过Gin Context）
func (e *ElasticsearchLogStorage) RecordSysLogToDBAndFileByGinContext(ginCtx interface{}, logType int, content string, prompt string) error {
	// 类型断言获取gin.Context
	ctx, ok := ginCtx.(*gin.Context)
	if !ok {
		return fmt.Errorf("invalid context type, expected *gin.Context")
	}

	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := ctx.GetString(ctxkey.ErrorCode)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return nil
		}

		// 获取用户ID
		userId := ctx.GetInt(ctxkey.Id)

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, ctx.GetString(ctxkey.RequestModel))
		if prompt != "" {
			prompt = TruncateOptimized(prompt, maxPromptLogLength, ctx.GetString(ctxkey.RequestModel))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}

	failedDuration := ctx.GetInt64(ctxkey.FailedDuration)
	var requestDuration int64
	var responseFirstByteDuration int64
	var totalDuration int64
	if failedDuration > 0 {
		requestDuration = failedDuration
		responseFirstByteDuration = failedDuration
		totalDuration = failedDuration
	}

	log := &Log{
		RequestId:                 ctx.GetString(helper.RequestIdKey),
		UserId:                    ctx.GetInt(ctxkey.Id),
		Username:                  CacheGetUsernameById(ctx.GetInt(ctxkey.Id)),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		TokenName:                 ctx.GetString(ctxkey.TokenName),
		TokenKey:                  ctx.GetString(ctxkey.TokenKey),
		TokenGroup:                ctx.GetString(ctxkey.TokenGroup),
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		ChannelName:               ctx.GetString(ctxkey.ChannelName),
		ModelName:                 ctx.GetString(ctxkey.RequestModel),
		ChannelId:                 ctx.GetInt(ctxkey.ChannelId),
		Ip:                        helper.GetClientRealIp(ctx),
		RemoteIp:                  ctx.RemoteIP(),
		ErrorCode:                 ctx.GetString(ctxkey.ErrorCode),
	}

	// 使用Elasticsearch的RecordLog方法
	err := e.RecordLog(context.Background(), log)
	if err != nil {
		logger.Error(nil, "<RecordSysLogToDBAndFileByGinContext> failed to record log: "+err.Error())
		return err
	}

	if prompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 获取用户ID
	userId := ctx.GetInt(ctxkey.Id)

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, maxPromptLogLength, ctx.GetString(ctxkey.RequestModel))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = e.RecordLogExtend(context.Background(), logExtend)
	if err != nil {
		logger.Error(nil, "<RecordSysLogToDBAndFileByGinContext> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// RecordLogToDBAndFileByMeta 记录日志到数据库和文件（通过Meta）
func (e *ElasticsearchLogStorage) RecordLogToDBAndFileByMeta(ctx context.Context, logType int, toFile bool, meta Meta, content string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64) error {
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := meta.ErrorCode

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		if meta.DetailPrompt != "" {
			meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	if toFile {
		switch logType {
		case LogTypeSystemInfo:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		case LogTypeSystemErr:
			logger.SysError(fmt.Sprintf("系统错误: %s", content))
		default:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		}
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	if ctx == nil {
		ctx = context.Background()
	}

	if meta.RequestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, meta.RequestId)
	}

	log := &Log{
		UserId:                    meta.UserId,
		Username:                  CacheGetUsernameById(meta.UserId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		PromptTokens:              meta.PromptTokens,
		CompletionTokens:          meta.CompletionTokens,
		TokenName:                 meta.TokenName,
		TokenKey:                  meta.TokenKey,
		TokenGroup:                meta.TokenGroup,
		ChannelName:               meta.ChannelName,
		ModelName:                 lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
		Quota:                     quota,
		ChannelId:                 meta.ChannelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  meta.IsStream,
		RequestId:                 meta.RequestId,
		Ip:                        meta.Ip,
		RemoteIp:                  meta.RemoteIp,
		ErrorCode:                 meta.ErrorCode,
	}

	// 使用Elasticsearch的RecordLog方法
	err := e.RecordLog(ctx, log)
	if err != nil {
		logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record log: "+err.Error())
		return err
	}

	if meta.DetailPrompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    meta.DetailPrompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = e.RecordLogExtend(ctx, logExtend)
	if err != nil {
		logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// TruncateLogExtendTable 清空LogExtend表
func (e *ElasticsearchLogStorage) TruncateLogExtendTable(ctx context.Context) error {
	// Elasticsearch使用删除所有文档的方式来模拟TRUNCATE
	// 构建删除所有文档的查询
	deleteQuery := map[string]interface{}{
		"query": map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
	}

	// 获取所有LogExtend索引
	indices, err := e.getLogExtendIndices()
	if err != nil {
		logger.Error(ctx, "<TruncateLogExtendTable> failed to get LogExtend indices: "+err.Error())
		return fmt.Errorf("failed to get LogExtend indices: %v", err)
	}

	// 如果没有索引，直接返回
	if len(indices) == 0 {
		logger.Info(ctx, "<TruncateLogExtendTable> no LogExtend indices found")
		return nil
	}

	// 删除所有LogExtend索引中的文档
	for _, indexName := range indices {
		err := e.makeRequest("POST", "/"+indexName+"/_delete_by_query", deleteQuery, nil)
		if err != nil {
			logger.Error(ctx, "<TruncateLogExtendTable> failed to delete documents from index "+indexName+": "+err.Error())
			// 继续处理其他索引，不立即返回错误
		} else {
			logger.SysLog(fmt.Sprintf("Successfully truncated LogExtend index: %s", indexName))
		}
	}

	logger.Info(ctx, "<TruncateLogExtendTable> successfully truncated log extend table")
	return nil
}
