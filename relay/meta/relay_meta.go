package meta

import (
	"strings"

	"github.com/songquanpeng/one-api/relay/apitype"

	"time"

	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/relaymode"
)

type RetryTimestamp struct {
	ChannelId int64 `json:"channel_id"`
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
	Duration  int64 `json:"duration"`
}

type Meta struct {
	IsSay1Matched                   bool
	FirstStartTime_                 int64
	StartTime_                      int64
	RequestEndTime_                 int64 // 响应首个字节的时间
	ResponseFirstByteTime_          int64 // 响应首个字节的时间
	RequestId                       string
	Ip                              string
	RemoteIp                        string
	CloudflareIp                    string
	XForwardedForIp                 string
	XRealIp                         string
	Mode                            int
	IsPackagePlan                   bool
	CustomFullURLEnabled            bool
	ArrangeMessages                 bool
	OriginalModelPricing            bool
	PackagePlanInstance             model.PackagePlanInstance
	BillingType                     int
	ChannelType                     int
	ChannelId                       int
	ChannelName                     string
	TokenId                         int
	TokenName                       string
	TokenKey                        string
	UserId                          int
	UserName                        string
	Group                           string
	TokenGroup                      string
	RequestModel                    string
	ModelFixedPrice                 float64
	ModelMapping                    map[string]string
	Base64ImagePrefixMapping        map[string]string
	ExtraHeaders                    map[string]string
	ModelMappingArr                 []map[string]string
	BaseURL                         string
	APIVersion                      string
	APIKey                          string
	APIType                         int
	Config                          model.ChannelConfig
	DetailPrompt                    string
	DetailCompletion                string
	UpstreamResponse                string // 上游返回的原始响应
	FullResponse                    string // 处理后返回给客户的最终响应
	CompletionId                    string
	IsStream                        bool
	OriginModelName                 string
	ActualModelName                 string
	RequestURLPath                  string
	PromptTokens                    int // only for DoResponse
	SystemPrompt                    string
	StartTime                       time.Time
	CompletionTokens                int
	ExcludeCustomPromptCostEnabled  bool
	UsageRecalculationEnabled       bool
	EmptyResponseErrorEnabled       bool
	RemoveImageDownloadErrorEnabled bool
	IsAdminPass                     bool
	ChannelTimeoutBreakerTime       int64
	IsNet                           bool
	LatestMessageContent            string
	RequestTokenLimitEnabled        bool
	MinRequestTokenCount            int64
	MaxRequestTokenCount            int64
	ClaudeStreamEnabled             bool
	KeyWordErrorEnabled             bool
	KeyWordError                    string
	TransparentProxyEnabled         bool
	NeedChangeOriginalRequest       bool
	IsAudio                         bool
	AudioPricePerMinute             float64
	AudioDurationSecs               int
	AudioDurationMinutes            int
	AudioCostUsd                    float64
	InputHasFunctionCall            bool
	InputHasImage                   bool
	UseChannel                      []int
	RequestDuration                 int64
	ResponseFirstByteDuration       int64
	TotalDuration                   int64
	Quota                           int64
	ForceO1StreamEnabled            bool
	CostPerUnit                     float64 // 渠道成本单价(每1美元的成本)
	Cost                            float64 // 本次请求的成本金额
	UpstreamModelRatio              float64
	ErrorCode                       string // 添加 ErrorCode 字段
	SupportStreamOptions            bool
	ShouldIncludeUsage              bool
	RetryTimestamps                 []RetryTimestamp // 记录所有重试渠道的时间信息
	IsV1MessagesPath                bool
	UserTrustUpstreamStreamUsage    int
	UserForceStreamOption           int
	UserForceDownstreamStreamUsage  int
}

func GetByContext(c *gin.Context) *Meta {
	modelMappingArrInterface, exists := c.Get("model_mapping_arr")
	modelMappingArr := make([]map[string]string, 0)
	if exists {
		if converted, ok := modelMappingArrInterface.([]map[string]string); ok {
			modelMappingArr = converted
		} else {
			// 如果类型转换失败，你可能需要处理错误，或者至少记录一条错误日志
			logger.SysError("model_mapping_arr is not of type []map[string]string")
		}
	}
	requestModel := c.GetString(ctxkey.RequestModel)
	isNet := false
	if strings.HasPrefix(c.GetString(ctxkey.RequestModel), "net-") {
		requestModel = strings.TrimPrefix(c.GetString(ctxkey.RequestModel), "net-")
		isNet = true
	}
	slice, b := helper.GetIntSlice(c, "use_channel")
	if !b {
		slice = []int{}
	}

	// 从 context 中获取已存在的 RetryTimestamps
	var retryTimestamps []RetryTimestamp
	if existingRetryTimestamps, exists := c.Get("retry_timestamps"); exists {
		if converted, ok := existingRetryTimestamps.([]RetryTimestamp); ok {
			retryTimestamps = converted
		}
	}

	ipInfo := helper.GetAllClientIPs(c)
	meta := Meta{
		FirstStartTime_:                 c.GetInt64(ctxkey.FirstStartTime),
		StartTime_:                      helper.GetTimestamp(),
		RequestId:                       c.GetString(helper.RequestIdKey),
		Ip:                              ipInfo.Ip,
		RemoteIp:                        ipInfo.RemoteIp,
		CloudflareIp:                    ipInfo.CloudflareIp,
		XForwardedForIp:                 ipInfo.XForwardedForIp,
		XRealIp:                         ipInfo.XRealIp,
		Mode:                            relaymode.GetByPath(c.Request.URL.Path, c),
		BillingType:                     c.GetInt(ctxkey.BillingType),
		IsPackagePlan:                   c.GetBool("is_package_plan"),
		CustomFullURLEnabled:            c.GetBool("custom_full_url_enabled"),
		OriginalModelPricing:            c.GetBool("original_model_pricing"),
		ArrangeMessages:                 c.GetBool("arrange_messages"),
		ChannelType:                     c.GetInt(ctxkey.Channel),
		ChannelId:                       c.GetInt(ctxkey.ChannelId),
		ChannelName:                     c.GetString(ctxkey.ChannelName),
		TokenId:                         c.GetInt(ctxkey.TokenId),
		TokenName:                       c.GetString(ctxkey.TokenName),
		TokenKey:                        c.GetString(ctxkey.TokenKey),
		UserId:                          c.GetInt(ctxkey.Id),
		Group:                           c.GetString(ctxkey.Group),
		TokenGroup:                      c.GetString(ctxkey.TokenGroup),
		RequestModel:                    c.GetString(ctxkey.RequestModel),
		ModelMapping:                    c.GetStringMapString(ctxkey.ModelMapping),
		Base64ImagePrefixMapping:        c.GetStringMapString(ctxkey.Base64ImagePrefixMapping),
		OriginModelName:                 requestModel,
		ModelMappingArr:                 modelMappingArr,
		ExtraHeaders:                    c.GetStringMapString("extra_headers"),
		BaseURL:                         c.GetString(ctxkey.BaseURL),
		APIVersion:                      c.GetString(common.ConfigKeyAPIVersion),
		APIKey:                          strings.TrimPrefix(c.Request.Header.Get("Authorization"), "Bearer "),
		RequestURLPath:                  c.Request.URL.String(),
		SystemPrompt:                    c.GetString(ctxkey.SystemPrompt),
		StartTime:                       time.Now(),
		ExcludeCustomPromptCostEnabled:  c.GetBool("exclude_custom_prompt_cost_enabled"),
		ChannelTimeoutBreakerTime:       c.GetInt64("channel_timeout_breaker_time"),
		UsageRecalculationEnabled:       c.GetBool("usage_recalculation_enabled"),
		EmptyResponseErrorEnabled:       c.GetBool("empty_response_error_enabled"),
		RemoveImageDownloadErrorEnabled: c.GetBool("remove_image_download_error_enabled"),
		IsNet:                           isNet,
		RequestTokenLimitEnabled:        c.GetBool("request_token_limit_enabled"),
		MinRequestTokenCount:            c.GetInt64("min_request_token_count"),
		MaxRequestTokenCount:            c.GetInt64("max_request_token_count"),
		ClaudeStreamEnabled:             c.GetBool("claude_stream_enabled"),
		KeyWordErrorEnabled:             c.GetBool("keyword_error_enabled"),
		KeyWordError:                    c.GetString("keyword_error"),
		TransparentProxyEnabled:         c.GetBool("transparent_proxy_enabled"),
		InputHasFunctionCall:            c.GetBool(ctxkey.InputHasFunctionCall),
		InputHasImage:                   c.GetBool(ctxkey.InputHasImage),
		UseChannel:                      slice,
		ForceO1StreamEnabled:            c.GetBool("force_o1_stream_enabled"),
		CostPerUnit:                     c.GetFloat64("cost_per_unit"),
		ErrorCode:                       c.GetString(ctxkey.ErrorCode),
		ShouldIncludeUsage:              c.GetBool(ctxkey.ShouldIncludeUsage),
		RetryTimestamps:                 retryTimestamps, // 使用从context中获取的值，如果没有则是空切片
		IsV1MessagesPath:                c.GetBool(ctxkey.IsV1MessagesPath),
	}
	// 获取用户级别的流式配置（使用缓存版本）
	userId := c.GetInt(ctxkey.Id)
	trustUpstream, forceOption, forceDownstream, _ := model.GetCacheUserStreamConfig(userId)
	meta.UserTrustUpstreamStreamUsage = trustUpstream
	meta.UserForceStreamOption = forceOption
	meta.UserForceDownstreamStreamUsage = forceDownstream

	if meta.ChannelType == channeltype.OpenAI || meta.ChannelType == channeltype.Anthropic ||
		meta.ChannelType == channeltype.AwsClaude || meta.ChannelType == channeltype.Gemini ||
		meta.ChannelType == channeltype.Cloudflare || meta.ChannelType == channeltype.Azure ||
		meta.ChannelType == channeltype.ShellAPI {
		meta.SupportStreamOptions = true
	}
	forceChatUrlEnabled := c.GetBool("force_chat_url_enabled")
	if forceChatUrlEnabled {
		meta.RequestURLPath = "/v1/chat/completions"
	}
	packagePlanInstance, exists := c.Get("package_plan_instance")
	if exists {
		if converted, ok := packagePlanInstance.(model.PackagePlanInstance); ok {
			meta.PackagePlanInstance = converted
		} else {
			logger.SysError("package_plan_instance is not of type model.PackagePlanInstance")
		}
	}
	cfg, ok := c.Get(ctxkey.Config)
	if ok {
		meta.Config = cfg.(model.ChannelConfig)
	}
	if meta.BaseURL == "" {
		meta.BaseURL = channeltype.ChannelBaseURLs[meta.ChannelType]
	}
	meta.APIType = channeltype.ToAPIType(meta.ChannelType)
	// 判断是否开启透传 并且 当前请求是/v1/messages 路径 并且APIType是OpenAI或者ShellAPI(类型其实都是0也就是OpenAI) 就修改meta.APIType为Anthropic
	if meta.TransparentProxyEnabled && meta.IsV1MessagesPath && meta.APIType == apitype.OpenAI {
		meta.APIType = apitype.Anthropic
	}
	return &meta
}

func (m *Meta) DeepCopy() *Meta {
	if m == nil {
		return nil
	}

	// 创建一个新的 Meta 对象
	cp := &Meta{
		StartTime_:                     m.StartTime_,
		Mode:                           m.Mode,
		IsPackagePlan:                  m.IsPackagePlan,
		CustomFullURLEnabled:           m.CustomFullURLEnabled,
		ArrangeMessages:                m.ArrangeMessages,
		OriginalModelPricing:           m.OriginalModelPricing,
		PackagePlanInstance:            m.PackagePlanInstance,
		BillingType:                    m.BillingType,
		ChannelType:                    m.ChannelType,
		ChannelId:                      m.ChannelId,
		ChannelName:                    m.ChannelName,
		TokenId:                        m.TokenId,
		TokenName:                      m.TokenName,
		TokenKey:                       m.TokenKey,
		UserId:                         m.UserId,
		Group:                          m.Group,
		TokenGroup:                     m.TokenGroup,
		RequestModel:                   m.RequestModel,
		ModelFixedPrice:                m.ModelFixedPrice,
		BaseURL:                        m.BaseURL,
		APIVersion:                     m.APIVersion,
		APIKey:                         m.APIKey,
		APIType:                        m.APIType,
		Config:                         m.Config,
		DetailPrompt:                   m.DetailPrompt,
		DetailCompletion:               m.DetailCompletion,
		FullResponse:                   m.FullResponse,
		CompletionId:                   m.CompletionId,
		IsStream:                       m.IsStream,
		OriginModelName:                m.OriginModelName,
		ActualModelName:                m.ActualModelName,
		RequestURLPath:                 m.RequestURLPath,
		PromptTokens:                   m.PromptTokens,
		ExcludeCustomPromptCostEnabled: m.ExcludeCustomPromptCostEnabled,
		RequestTokenLimitEnabled:       m.RequestTokenLimitEnabled,
		MinRequestTokenCount:           m.MinRequestTokenCount,
		MaxRequestTokenCount:           m.MaxRequestTokenCount,
		ForceO1StreamEnabled:           m.ForceO1StreamEnabled,
		ErrorCode:                      m.ErrorCode,
		IsV1MessagesPath:               m.IsV1MessagesPath,
	}

	// 深拷贝 ModelMapping
	if m.ModelMapping != nil {
		cp.ModelMapping = make(map[string]string)
		for k, v := range m.ModelMapping {
			cp.ModelMapping[k] = v
		}
	}

	// 深拷贝 Base64ImagePrefixMapping
	if m.Base64ImagePrefixMapping != nil {
		cp.Base64ImagePrefixMapping = make(map[string]string)
		for k, v := range m.Base64ImagePrefixMapping {
			cp.Base64ImagePrefixMapping[k] = v
		}
	}

	// 深拷贝 ExtraHeaders
	if m.ExtraHeaders != nil {
		cp.ExtraHeaders = make(map[string]string)
		for k, v := range m.ExtraHeaders {
			cp.ExtraHeaders[k] = v
		}
	}

	// 深拷贝 ModelMappingArr
	if m.ModelMappingArr != nil {
		cp.ModelMappingArr = make([]map[string]string, len(m.ModelMappingArr))
		for i, mm := range m.ModelMappingArr {
			cp.ModelMappingArr[i] = make(map[string]string)
			for k, v := range mm {
				cp.ModelMappingArr[i][k] = v
			}
		}
	}

	return cp
}

func (m *Meta) DeepCopyToLogMeta() model.Meta {
	if m == nil {
		return model.Meta{}
	}

	// 创建一个新的 Meta 对象
	cp := model.Meta{
		StartTime_:                      m.StartTime_,
		RequestId:                       m.RequestId,
		Ip:                              m.Ip,
		RemoteIp:                        m.RemoteIp,
		Mode:                            m.Mode,
		IsPackagePlan:                   m.IsPackagePlan,
		CustomFullURLEnabled:            m.CustomFullURLEnabled,
		ArrangeMessages:                 m.ArrangeMessages,
		OriginalModelPricing:            m.OriginalModelPricing,
		PackagePlanInstance:             m.PackagePlanInstance,
		BillingType:                     m.BillingType,
		ChannelType:                     m.ChannelType,
		ChannelId:                       m.ChannelId,
		ChannelName:                     m.ChannelName,
		TokenId:                         m.TokenId,
		TokenName:                       m.TokenName,
		TokenKey:                        m.TokenKey,
		UserId:                          m.UserId,
		UserName:                        m.UserName,
		Group:                           m.Group,
		TokenGroup:                      m.TokenGroup,
		RequestModel:                    m.RequestModel,
		ModelFixedPrice:                 m.ModelFixedPrice,
		BaseURL:                         m.BaseURL,
		APIVersion:                      m.APIVersion,
		APIKey:                          m.APIKey,
		APIType:                         m.APIType,
		Config:                          m.Config,
		DetailPrompt:                    m.DetailPrompt,
		DetailCompletion:                m.DetailCompletion,
		FullResponse:                    m.FullResponse,
		CompletionId:                    m.CompletionId,
		IsStream:                        m.IsStream,
		OriginModelName:                 m.OriginModelName,
		ActualModelName:                 m.ActualModelName,
		RequestURLPath:                  m.RequestURLPath,
		PromptTokens:                    m.PromptTokens,
		CompletionTokens:                m.CompletionTokens,
		ExcludeCustomPromptCostEnabled:  m.ExcludeCustomPromptCostEnabled,
		RequestTokenLimitEnabled:        m.RequestTokenLimitEnabled,
		MinRequestTokenCount:            m.MinRequestTokenCount,
		MaxRequestTokenCount:            m.MaxRequestTokenCount,
		ChannelTimeoutBreakerTime:       m.ChannelTimeoutBreakerTime,
		EmptyResponseErrorEnabled:       m.EmptyResponseErrorEnabled,
		RemoveImageDownloadErrorEnabled: m.RemoveImageDownloadErrorEnabled,
		RequestDuration:                 m.RequestDuration,
		ResponseFirstByteDuration:       m.ResponseFirstByteDuration,
		TotalDuration:                   m.TotalDuration,
		Quota:                           m.Quota,
		ForceO1StreamEnabled:            m.ForceO1StreamEnabled,
		ErrorCode:                       m.ErrorCode,
		IsV1MessagesPath:                m.IsV1MessagesPath,
	}

	// 深拷贝 ModelMapping
	if m.ModelMapping != nil {
		cp.ModelMapping = make(map[string]string)
		for k, v := range m.ModelMapping {
			cp.ModelMapping[k] = v
		}
	}

	// 深拷贝 Base64ImagePrefixMapping
	if m.Base64ImagePrefixMapping != nil {
		cp.Base64ImagePrefixMapping = make(map[string]string)
		for k, v := range m.Base64ImagePrefixMapping {
			cp.Base64ImagePrefixMapping[k] = v
		}
	}

	// 深拷贝 ExtraHeaders
	if m.ExtraHeaders != nil {
		cp.ExtraHeaders = make(map[string]string)
		for k, v := range m.ExtraHeaders {
			cp.ExtraHeaders[k] = v
		}
	}

	// 深拷贝 ModelMappingArr
	if m.ModelMappingArr != nil {
		cp.ModelMappingArr = make([]map[string]string, len(m.ModelMappingArr))
		for i, mm := range m.ModelMappingArr {
			cp.ModelMappingArr[i] = make(map[string]string)
			for k, v := range mm {
				cp.ModelMappingArr[i][k] = v
			}
		}
	}

	return cp
}

// GetTrustUpstreamStreamUsageEnabled 获取是否信任上游流式用量统计，优先级：用户 > 渠道 > 全局
func (m *Meta) GetTrustUpstreamStreamUsageEnabled() bool {
	if m.UserTrustUpstreamStreamUsage != 0 {
		return m.UserTrustUpstreamStreamUsage == 1
	}
	return m.Config.GetTrustUpstreamStreamUsageEnabled()
}

// GetForceStreamOptionEnabled 获取是否强制要求上游返回用量，优先级：用户 > 渠道 > 全局
func (m *Meta) GetForceStreamOptionEnabled() bool {
	if m.UserForceStreamOption != 0 {
		return m.UserForceStreamOption == 1
	}
	return m.Config.GetForceStreamOptionEnabled()
}

// GetForceDownstreamStreamUsageEnabled 获取是否强制返回下游流式用量，优先级：用户 > 渠道 > 全局
func (m *Meta) GetForceDownstreamStreamUsageEnabled() bool {
	if m.UserForceDownstreamStreamUsage != 0 {
		return m.UserForceDownstreamStreamUsage == 1
	}
	return m.Config.GetForceDownstreamStreamUsageEnabled()
}

// GetMockOpenAICompleteFormatEnabled 获取用户是否开启了模拟OpenAI官方响应格式的选项
// 首先检查用户级别的配置，如果未配置则使用系统级别的配置
func (m *Meta) GetMockOpenAICompleteFormatEnabled() bool {
	// 先尝试获取用户级别的配置
	userSetting, err := model.CacheGetUserMockOpenAICompleteFormat(m.UserId)
	if err != nil {
		// 如果获取用户级别配置出错，则使用系统级别的配置
		return config.MockOpenAICompleteFormatEnabled
	}

	// 用户配置值说明:
	// 0: 使用系统配置
	// 1: 启用
	// 2: 禁用
	switch userSetting {
	case 0:
		return config.MockOpenAICompleteFormatEnabled
	case 1:
		return true
	case 2:
		return false
	default:
		return config.MockOpenAICompleteFormatEnabled
	}
}

func (m *Meta) IsImageModeNeedReturnUsage() bool {
	return (m.Mode == relaymode.ImagesEdits || m.Mode == relaymode.ImagesGenerations) && !strings.HasPrefix(m.ActualModelName, "dall-e")
}
