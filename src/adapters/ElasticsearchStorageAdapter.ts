import { Client } from '@elastic/elasticsearch';
import { IStorageAdapter, StorageConfig } from './interfaces/IStorageAdapter';
import { WebhookMessage, WebhookQueryParams, WebhookListResponse } from '../types/webhook';
import { v4 as uuidv4 } from 'uuid';

export class ElasticsearchStorageAdapter implements IStorageAdapter {
  private client: Client;
  private indexName: string;

  constructor(private config: StorageConfig) {
    this.indexName = config.indexName || 'webhook-messages';
    
    this.client = new Client({
      node: config.connectionString || `http://${config.host || 'localhost'}:${config.port || 9200}`,
      auth: config.username && config.password ? {
        username: config.username,
        password: config.password,
      } : undefined,
      tls: config.ssl ? {
        rejectUnauthorized: false,
      } : undefined,
      requestTimeout: config.timeout || 30000,
      maxRetries: 3,
    });
  }

  async initialize(): Promise<void> {
    try {
      // Check if Elasticsearch is available
      await this.client.ping();
      
      // Create index if it doesn't exist
      const indexExists = await this.client.indices.exists({
        index: this.indexName,
      });

      if (!indexExists) {
        await this.createIndex();
      }

      console.log('✅ Elasticsearch storage adapter initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Elasticsearch storage adapter:', error);
      throw error;
    }
  }

  private async createIndex(): Promise<void> {
    await this.client.indices.create({
      index: this.indexName,
      body: {
        settings: {
          number_of_shards: 1,
          number_of_replicas: 0,
          analysis: {
            analyzer: {
              webhook_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'stop'],
              },
            },
          },
        },
        mappings: {
          properties: {
            id: { type: 'keyword' },
            type: { type: 'keyword' },
            title: { 
              type: 'text',
              analyzer: 'webhook_analyzer',
              fields: {
                keyword: { type: 'keyword' }
              }
            },
            content: { 
              type: 'text',
              analyzer: 'webhook_analyzer'
            },
            values: { type: 'object', enabled: false },
            timestamp: { type: 'long' },
            receivedAt: { type: 'date' },
            userAgent: { type: 'text' },
            sourceIp: { type: 'ip' },
            signature: { type: 'keyword' },
            processed: { type: 'boolean' },
          },
        },
      },
    });
  }

  async storeMessage(message: Omit<WebhookMessage, 'id' | 'receivedAt'>): Promise<WebhookMessage> {
    const id = uuidv4();
    const receivedAt = new Date();
    
    const doc = {
      id,
      ...message,
      receivedAt,
    };

    await this.client.index({
      index: this.indexName,
      id,
      body: doc,
      refresh: 'wait_for', // Ensure document is searchable immediately
    });

    return doc as WebhookMessage;
  }

  async storeMessages(messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[]): Promise<WebhookMessage[]> {
    const body = [];
    const results: WebhookMessage[] = [];

    for (const message of messages) {
      const id = uuidv4();
      const receivedAt = new Date();
      const doc = { id, ...message, receivedAt };

      body.push({ index: { _index: this.indexName, _id: id } });
      body.push(doc);
      results.push(doc as WebhookMessage);
    }

    await this.client.bulk({
      body,
      refresh: 'wait_for',
    });

    return results;
  }

  async getMessages(params: WebhookQueryParams): Promise<WebhookListResponse> {
    const {
      page = 1,
      pageSize = 20,
      type,
      search,
      startDate,
      endDate,
      processed
    } = params;

    const from = (page - 1) * pageSize;
    const size = Math.min(pageSize, 100);

    const query: any = {
      bool: {
        must: [],
        filter: [],
      },
    };

    if (type) {
      query.bool.filter.push({ term: { type } });
    }

    if (typeof processed === 'boolean') {
      query.bool.filter.push({ term: { processed } });
    }

    if (startDate || endDate) {
      const range: any = {};
      if (startDate) range.gte = startDate;
      if (endDate) range.lte = endDate;
      query.bool.filter.push({ range: { receivedAt: range } });
    }

    if (search) {
      query.bool.must.push({
        multi_match: {
          query: search,
          fields: ['title^2', 'content', 'type'],
          type: 'best_fields',
          fuzziness: 'AUTO',
        },
      });
    }

    const response = await this.client.search({
      index: this.indexName,
      body: {
        query: query.bool.must.length === 0 && query.bool.filter.length === 0 
          ? { match_all: {} } 
          : query,
        sort: [{ receivedAt: { order: 'desc' } }],
        from,
        size,
      },
    });

    const messages = response.body.hits.hits.map((hit: any) => hit._source);
    const total = response.body.hits.total.value;

    return {
      messages,
      total,
      page,
      pageSize: size,
      totalPages: Math.ceil(total / size),
    };
  }

  async getMessageById(id: string): Promise<WebhookMessage | null> {
    try {
      const response = await this.client.get({
        index: this.indexName,
        id,
      });

      return response.body._source;
    } catch (error: any) {
      if (error.statusCode === 404) {
        return null;
      }
      throw error;
    }
  }

  async updateMessage(id: string, updates: Partial<WebhookMessage>): Promise<WebhookMessage> {
    await this.client.update({
      index: this.indexName,
      id,
      body: {
        doc: updates,
      },
      refresh: 'wait_for',
    });

    const updated = await this.getMessageById(id);
    if (!updated) {
      throw new Error('Message not found after update');
    }

    return updated;
  }

  async deleteMessage(id: string): Promise<void> {
    await this.client.delete({
      index: this.indexName,
      id,
      refresh: 'wait_for',
    });
  }

  async getStats() {
    const response = await this.client.search({
      index: this.indexName,
      body: {
        size: 0,
        aggs: {
          total: {
            value_count: { field: 'id' },
          },
          by_type: {
            terms: { field: 'type', size: 50 },
          },
          last_24_hours: {
            filter: {
              range: {
                receivedAt: {
                  gte: 'now-24h',
                },
              },
            },
          },
        },
      },
    });

    const aggs = response.body.aggregations;

    return {
      total: aggs.total.value,
      byType: aggs.by_type.buckets.map((bucket: any) => ({
        type: bucket.key,
        count: bucket.doc_count,
      })),
      last24Hours: aggs.last_24_hours.doc_count,
    };
  }

  async searchMessages(query: string, params?: WebhookQueryParams): Promise<WebhookListResponse> {
    return this.getMessages({
      ...params,
      search: query,
    });
  }

  async cleanupOldMessages(olderThanDays: number): Promise<number> {
    const response = await this.client.deleteByQuery({
      index: this.indexName,
      body: {
        query: {
          range: {
            receivedAt: {
              lt: `now-${olderThanDays}d`,
            },
          },
        },
      },
      refresh: true,
    });

    return response.body.deleted;
  }

  async getHealthStatus() {
    try {
      const health = await this.client.cluster.health();
      const status = health.body.status;

      return {
        status: status === 'green' ? 'healthy' as const : 
                status === 'yellow' ? 'degraded' as const : 'unhealthy' as const,
        details: {
          cluster_status: status,
          number_of_nodes: health.body.number_of_nodes,
          active_shards: health.body.active_shards,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy' as const,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  async close(): Promise<void> {
    await this.client.close();
  }
}
