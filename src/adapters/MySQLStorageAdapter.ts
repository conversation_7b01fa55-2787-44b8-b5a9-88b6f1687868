import mysql from 'mysql2/promise';
import { IStorageAdapter, StorageConfig } from './interfaces/IStorageAdapter';
import { WebhookMessage, WebhookQueryParams, WebhookListResponse } from '../types/webhook';
import { v4 as uuidv4 } from 'uuid';

export class MySQLStorageAdapter implements IStorageAdapter {
  private pool: mysql.Pool;

  constructor(private config: StorageConfig) {
    this.pool = mysql.createPool({
      host: config.host || 'localhost',
      port: config.port || 3306,
      user: config.username,
      password: config.password,
      database: config.database,
      connectionLimit: config.maxConnections || 10,
      acquireTimeout: config.timeout || 60000,
      ssl: config.ssl,
      charset: 'utf8mb4',
    });
  }

  async initialize(): Promise<void> {
    try {
      // Test connection
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      // Create table if it doesn't exist
      await this.createTable();

      console.log('✅ MySQL storage adapter initialized');
    } catch (error) {
      console.error('❌ Failed to initialize MySQL storage adapter:', error);
      throw error;
    }
  }

  private async createTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS webhook_messages (
        id VARCHAR(36) PRIMARY KEY,
        type VARCHAR(100) NOT NULL,
        title TEXT NOT NULL,
        content LONGTEXT NOT NULL,
        values JSON,
        timestamp BIGINT NOT NULL,
        received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_agent TEXT,
        source_ip VARCHAR(45),
        signature VARCHAR(255),
        processed BOOLEAN DEFAULT FALSE,
        INDEX idx_type (type),
        INDEX idx_timestamp (timestamp),
        INDEX idx_received_at (received_at),
        INDEX idx_processed (processed),
        FULLTEXT idx_search (title, content)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    await this.pool.execute(createTableSQL);
  }

  async storeMessage(message: Omit<WebhookMessage, 'id' | 'receivedAt'>): Promise<WebhookMessage> {
    const id = uuidv4();
    const receivedAt = new Date();

    const sql = `
      INSERT INTO webhook_messages 
      (id, type, title, content, \`values\`, timestamp, received_at, user_agent, source_ip, signature, processed)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await this.pool.execute(sql, [
      id,
      message.type,
      message.title,
      message.content,
      message.values ? JSON.stringify(message.values) : null,
      message.timestamp,
      receivedAt,
      message.userAgent,
      message.sourceIp,
      message.signature,
      message.processed || false,
    ]);

    return {
      id,
      ...message,
      receivedAt,
    } as WebhookMessage;
  }

  async storeMessages(messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[]): Promise<WebhookMessage[]> {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();

      const sql = `
        INSERT INTO webhook_messages 
        (id, type, title, content, \`values\`, timestamp, received_at, user_agent, source_ip, signature, processed)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const results: WebhookMessage[] = [];

      for (const message of messages) {
        const id = uuidv4();
        const receivedAt = new Date();

        await connection.execute(sql, [
          id,
          message.type,
          message.title,
          message.content,
          message.values ? JSON.stringify(message.values) : null,
          message.timestamp,
          receivedAt,
          message.userAgent,
          message.sourceIp,
          message.signature,
          message.processed || false,
        ]);

        results.push({
          id,
          ...message,
          receivedAt,
        } as WebhookMessage);
      }

      await connection.commit();
      return results;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  async getMessages(params: WebhookQueryParams): Promise<WebhookListResponse> {
    const {
      page = 1,
      pageSize = 20,
      type,
      search,
      startDate,
      endDate,
      processed
    } = params;

    const offset = (page - 1) * pageSize;
    const limit = Math.min(pageSize, 100);

    let whereClause = 'WHERE 1=1';
    const queryParams: any[] = [];

    if (type) {
      whereClause += ' AND type = ?';
      queryParams.push(type);
    }

    if (typeof processed === 'boolean') {
      whereClause += ' AND processed = ?';
      queryParams.push(processed);
    }

    if (startDate) {
      whereClause += ' AND received_at >= ?';
      queryParams.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND received_at <= ?';
      queryParams.push(endDate);
    }

    if (search) {
      whereClause += ' AND (MATCH(title, content) AGAINST(? IN NATURAL LANGUAGE MODE) OR type LIKE ?)';
      queryParams.push(search, `%${search}%`);
    }

    // Get total count
    const countSQL = `SELECT COUNT(*) as total FROM webhook_messages ${whereClause}`;
    const [countRows] = await this.pool.execute(countSQL, queryParams) as any;
    const total = countRows[0].total;

    // Get messages
    const messagesSQL = `
      SELECT id, type, title, content, \`values\`, timestamp, received_at, 
             user_agent, source_ip, signature, processed
      FROM webhook_messages 
      ${whereClause}
      ORDER BY received_at DESC
      LIMIT ? OFFSET ?
    `;

    const [rows] = await this.pool.execute(messagesSQL, [...queryParams, limit, offset]) as any;

    const messages = rows.map(this.convertMySQLMessage);

    return {
      messages,
      total,
      page,
      pageSize: limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getMessageById(id: string): Promise<WebhookMessage | null> {
    const sql = `
      SELECT id, type, title, content, \`values\`, timestamp, received_at,
             user_agent, source_ip, signature, processed
      FROM webhook_messages 
      WHERE id = ?
    `;

    const [rows] = await this.pool.execute(sql, [id]) as any;

    if (rows.length === 0) {
      return null;
    }

    return this.convertMySQLMessage(rows[0]);
  }

  async updateMessage(id: string, updates: Partial<WebhookMessage>): Promise<WebhookMessage> {
    const setClause: string[] = [];
    const params: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'id' || key === 'receivedAt') return;
      
      const columnName = key === 'userAgent' ? 'user_agent' :
                        key === 'sourceIp' ? 'source_ip' :
                        key === 'values' ? '`values`' : key;
      
      setClause.push(`${columnName} = ?`);
      
      if (key === 'values' && value && typeof value !== 'string') {
        params.push(JSON.stringify(value));
      } else {
        params.push(value);
      }
    });

    if (setClause.length === 0) {
      throw new Error('No valid fields to update');
    }

    const sql = `UPDATE webhook_messages SET ${setClause.join(', ')} WHERE id = ?`;
    params.push(id);

    await this.pool.execute(sql, params);

    const updated = await this.getMessageById(id);
    if (!updated) {
      throw new Error('Message not found after update');
    }

    return updated;
  }

  async deleteMessage(id: string): Promise<void> {
    const sql = 'DELETE FROM webhook_messages WHERE id = ?';
    await this.pool.execute(sql, [id]);
  }

  async getStats() {
    const [totalRows] = await this.pool.execute('SELECT COUNT(*) as total FROM webhook_messages') as any;
    const total = totalRows[0].total;

    const [typeRows] = await this.pool.execute(`
      SELECT type, COUNT(*) as count 
      FROM webhook_messages 
      GROUP BY type 
      ORDER BY count DESC
    `) as any;

    const [recentRows] = await this.pool.execute(`
      SELECT COUNT(*) as count 
      FROM webhook_messages 
      WHERE received_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `) as any;

    return {
      total,
      byType: typeRows.map((row: any) => ({
        type: row.type,
        count: row.count,
      })),
      last24Hours: recentRows[0].count,
    };
  }

  async searchMessages(query: string, params?: WebhookQueryParams): Promise<WebhookListResponse> {
    return this.getMessages({
      ...params,
      search: query,
    });
  }

  async cleanupOldMessages(olderThanDays: number): Promise<number> {
    const sql = `
      DELETE FROM webhook_messages 
      WHERE received_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;

    const [result] = await this.pool.execute(sql, [olderThanDays]) as any;
    return result.affectedRows;
  }

  async getHealthStatus() {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      return { status: 'healthy' as const };
    } catch (error) {
      return {
        status: 'unhealthy' as const,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
  }

  private convertMySQLMessage(row: any): WebhookMessage {
    return {
      id: row.id,
      type: row.type,
      title: row.title,
      content: row.content,
      values: row.values ? JSON.parse(row.values) : null,
      timestamp: Number(row.timestamp),
      receivedAt: row.received_at,
      userAgent: row.user_agent,
      sourceIp: row.source_ip,
      signature: row.signature,
      processed: Boolean(row.processed),
    };
  }
}
