import { PrismaClient } from '@prisma/client';
import { IStorageAdapter, StorageConfig } from './interfaces/IStorageAdapter';
import { WebhookMessage, WebhookQueryParams, WebhookListResponse } from '../types/webhook';
import { v4 as uuidv4 } from 'uuid';

export class PrismaStorageAdapter implements IStorageAdapter {
  private prisma: PrismaClient;

  constructor(private config: StorageConfig) {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: config.connectionString || process.env.DATABASE_URL,
        },
      },
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    });
  }

  async initialize(): Promise<void> {
    try {
      await this.prisma.$connect();
      console.log('✅ Prisma storage adapter initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Prisma storage adapter:', error);
      throw error;
    }
  }

  async storeMessage(message: Omit<WebhookMessage, 'id' | 'receivedAt'>): Promise<WebhookMessage> {
    const stored = await this.prisma.webhookMessage.create({
      data: {
        id: uuidv4(),
        type: message.type,
        title: message.title,
        content: message.content,
        values: message.values ? JSON.stringify(message.values) : null,
        timestamp: BigInt(message.timestamp),
        userAgent: message.userAgent,
        sourceIp: message.sourceIp,
        signature: message.signature,
        processed: message.processed || false,
      },
    });

    return this.convertPrismaMessage(stored);
  }

  async storeMessages(messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[]): Promise<WebhookMessage[]> {
    const data = messages.map(message => ({
      id: uuidv4(),
      type: message.type,
      title: message.title,
      content: message.content,
      values: message.values ? JSON.stringify(message.values) : null,
      timestamp: BigInt(message.timestamp),
      userAgent: message.userAgent,
      sourceIp: message.sourceIp,
      signature: message.signature,
      processed: message.processed || false,
    }));

    // Use transaction for batch insert
    const stored = await this.prisma.$transaction(
      data.map(item => this.prisma.webhookMessage.create({ data: item }))
    );

    return stored.map(this.convertPrismaMessage);
  }

  async getMessages(params: WebhookQueryParams): Promise<WebhookListResponse> {
    const {
      page = 1,
      pageSize = 20,
      type,
      search,
      startDate,
      endDate,
      processed
    } = params;

    const skip = (page - 1) * pageSize;
    const take = Math.min(pageSize, 100);

    const where: any = {};

    if (type) {
      where.type = type;
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { type: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (startDate || endDate) {
      where.receivedAt = {};
      if (startDate) {
        where.receivedAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.receivedAt.lte = new Date(endDate);
      }
    }

    if (typeof processed === 'boolean') {
      where.processed = processed;
    }

    const [total, messages] = await Promise.all([
      this.prisma.webhookMessage.count({ where }),
      this.prisma.webhookMessage.findMany({
        where,
        skip,
        take,
        orderBy: { receivedAt: 'desc' },
      }),
    ]);

    return {
      messages: messages.map(this.convertPrismaMessage),
      total,
      page,
      pageSize: take,
      totalPages: Math.ceil(total / take),
    };
  }

  async getMessageById(id: string): Promise<WebhookMessage | null> {
    const message = await this.prisma.webhookMessage.findUnique({
      where: { id },
    });

    return message ? this.convertPrismaMessage(message) : null;
  }

  async updateMessage(id: string, updates: Partial<WebhookMessage>): Promise<WebhookMessage> {
    const updateData: any = { ...updates };
    
    if (updateData.values && typeof updateData.values !== 'string') {
      updateData.values = JSON.stringify(updateData.values);
    }
    
    if (updateData.timestamp) {
      updateData.timestamp = BigInt(updateData.timestamp);
    }

    const updated = await this.prisma.webhookMessage.update({
      where: { id },
      data: updateData,
    });

    return this.convertPrismaMessage(updated);
  }

  async deleteMessage(id: string): Promise<void> {
    await this.prisma.webhookMessage.delete({
      where: { id },
    });
  }

  async getStats() {
    const [total, byType, recent] = await Promise.all([
      this.prisma.webhookMessage.count(),
      this.prisma.webhookMessage.groupBy({
        by: ['type'],
        _count: { type: true },
        orderBy: { _count: { type: 'desc' } },
      }),
      this.prisma.webhookMessage.count({
        where: {
          receivedAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
        },
      }),
    ]);

    return {
      total,
      byType: byType.map(item => ({
        type: item.type,
        count: item._count.type,
      })),
      last24Hours: recent,
    };
  }

  async searchMessages(query: string, params?: WebhookQueryParams): Promise<WebhookListResponse> {
    // For Prisma, we'll use the same search logic as getMessages
    return this.getMessages({
      ...params,
      search: query,
    });
  }

  async cleanupOldMessages(olderThanDays: number): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
    
    const result = await this.prisma.webhookMessage.deleteMany({
      where: {
        receivedAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }

  async getHealthStatus() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy' as const };
    } catch (error) {
      return {
        status: 'unhealthy' as const,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  async close(): Promise<void> {
    await this.prisma.$disconnect();
  }

  private convertPrismaMessage(prismaMessage: any): WebhookMessage {
    return {
      ...prismaMessage,
      timestamp: Number(prismaMessage.timestamp),
      values: prismaMessage.values ? JSON.parse(prismaMessage.values) : null,
    };
  }
}
