import { IStorageAdapter, StorageConfig } from './interfaces/IStorageAdapter';
import { PrismaStorageAdapter } from './PrismaStorageAdapter';
import { ElasticsearchStorageAdapter } from './ElasticsearchStorageAdapter';
import { MySQLStorageAdapter } from './MySQLStorageAdapter';

export class StorageAdapterFactory {
  private static adapters = new Map<string, IStorageAdapter>();

  public static async createAdapter(config: StorageConfig): Promise<IStorageAdapter> {
    const key = this.generateAdapterKey(config);
    
    // Return existing adapter if available
    if (this.adapters.has(key)) {
      return this.adapters.get(key)!;
    }

    let adapter: IStorageAdapter;

    switch (config.type) {
      case 'sqlite':
        adapter = new PrismaStorageAdapter(config);
        break;
      
      case 'mysql':
        adapter = new MySQLStorageAdapter(config);
        break;
      
      case 'postgresql':
        // For now, use Prisma for PostgreSQL
        adapter = new PrismaStorageAdapter(config);
        break;
      
      case 'elasticsearch':
        adapter = new ElasticsearchStorageAdapter(config);
        break;
      
      default:
        throw new Error(`Unsupported storage type: ${config.type}`);
    }

    await adapter.initialize();
    this.adapters.set(key, adapter);
    
    return adapter;
  }

  public static async closeAdapter(config: StorageConfig): Promise<void> {
    const key = this.generateAdapterKey(config);
    const adapter = this.adapters.get(key);
    
    if (adapter) {
      await adapter.close();
      this.adapters.delete(key);
    }
  }

  public static async closeAllAdapters(): Promise<void> {
    const closePromises = Array.from(this.adapters.values()).map(adapter => adapter.close());
    await Promise.all(closePromises);
    this.adapters.clear();
  }

  private static generateAdapterKey(config: StorageConfig): string {
    const keyParts = [
      config.type,
      config.connectionString || '',
      config.host || '',
      config.port || '',
      config.database || '',
      config.username || '',
    ];
    
    return keyParts.join('|');
  }
}

export class FallbackStorageAdapter implements IStorageAdapter {
  constructor(
    private primaryAdapter: IStorageAdapter,
    private fallbackAdapter: IStorageAdapter
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.primaryAdapter.initialize();
    } catch (error) {
      console.warn('Primary storage initialization failed, using fallback:', error);
      await this.fallbackAdapter.initialize();
    }
  }

  async storeMessage(message: any): Promise<any> {
    try {
      return await this.primaryAdapter.storeMessage(message);
    } catch (error) {
      console.warn('Primary storage failed, using fallback for storeMessage:', error);
      return await this.fallbackAdapter.storeMessage(message);
    }
  }

  async storeMessages(messages: any[]): Promise<any[]> {
    try {
      return await this.primaryAdapter.storeMessages(messages);
    } catch (error) {
      console.warn('Primary storage failed, using fallback for storeMessages:', error);
      return await this.fallbackAdapter.storeMessages(messages);
    }
  }

  async getMessages(params: any): Promise<any> {
    try {
      return await this.primaryAdapter.getMessages(params);
    } catch (error) {
      console.warn('Primary storage failed, using fallback for getMessages:', error);
      return await this.fallbackAdapter.getMessages(params);
    }
  }

  async getMessageById(id: string): Promise<any> {
    try {
      return await this.primaryAdapter.getMessageById(id);
    } catch (error) {
      console.warn('Primary storage failed, using fallback for getMessageById:', error);
      return await this.fallbackAdapter.getMessageById(id);
    }
  }

  async updateMessage(id: string, updates: any): Promise<any> {
    try {
      const result = await this.primaryAdapter.updateMessage(id, updates);
      // Try to sync to fallback (best effort)
      try {
        await this.fallbackAdapter.updateMessage(id, updates);
      } catch (syncError) {
        console.warn('Failed to sync update to fallback storage:', syncError);
      }
      return result;
    } catch (error) {
      console.warn('Primary storage failed, using fallback for updateMessage:', error);
      return await this.fallbackAdapter.updateMessage(id, updates);
    }
  }

  async deleteMessage(id: string): Promise<void> {
    try {
      await this.primaryAdapter.deleteMessage(id);
      // Try to sync to fallback (best effort)
      try {
        await this.fallbackAdapter.deleteMessage(id);
      } catch (syncError) {
        console.warn('Failed to sync deletion to fallback storage:', syncError);
      }
    } catch (error) {
      console.warn('Primary storage failed, using fallback for deleteMessage:', error);
      await this.fallbackAdapter.deleteMessage(id);
    }
  }

  async getStats(): Promise<any> {
    try {
      return await this.primaryAdapter.getStats();
    } catch (error) {
      console.warn('Primary storage failed, using fallback for getStats:', error);
      return await this.fallbackAdapter.getStats();
    }
  }

  async searchMessages(query: string, params?: any): Promise<any> {
    try {
      return await this.primaryAdapter.searchMessages(query, params);
    } catch (error) {
      console.warn('Primary storage failed, using fallback for searchMessages:', error);
      return await this.fallbackAdapter.searchMessages(query, params);
    }
  }

  async cleanupOldMessages(olderThanDays: number): Promise<number> {
    let primaryResult = 0;
    let fallbackResult = 0;

    try {
      primaryResult = await this.primaryAdapter.cleanupOldMessages(olderThanDays);
    } catch (error) {
      console.warn('Primary storage cleanup failed:', error);
    }

    try {
      fallbackResult = await this.fallbackAdapter.cleanupOldMessages(olderThanDays);
    } catch (error) {
      console.warn('Fallback storage cleanup failed:', error);
    }

    return Math.max(primaryResult, fallbackResult);
  }

  async getHealthStatus(): Promise<any> {
    const [primaryHealth, fallbackHealth] = await Promise.allSettled([
      this.primaryAdapter.getHealthStatus(),
      this.fallbackAdapter.getHealthStatus(),
    ]);

    return {
      status: primaryHealth.status === 'fulfilled' && primaryHealth.value.status === 'healthy' 
        ? 'healthy' : 'degraded',
      details: {
        primary: primaryHealth.status === 'fulfilled' ? primaryHealth.value : { error: primaryHealth.reason },
        fallback: fallbackHealth.status === 'fulfilled' ? fallbackHealth.value : { error: fallbackHealth.reason },
      },
    };
  }

  async close(): Promise<void> {
    await Promise.all([
      this.primaryAdapter.close(),
      this.fallbackAdapter.close(),
    ]);
  }
}
