#!/usr/bin/env node

import { Command } from 'commander';
import { DataMigrationTool, MigrationOptions } from '../tools/DataMigration';
import { StorageConfig } from '../adapters/interfaces/IStorageAdapter';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const program = new Command();

program
  .name('webhook-migrate')
  .description('Data migration tool for webhook messages')
  .version('1.0.0');

program
  .command('migrate')
  .description('Migrate data between storage systems')
  .requiredOption('--source-type <type>', 'Source storage type (sqlite|mysql|postgresql|elasticsearch)')
  .requiredOption('--target-type <type>', 'Target storage type (sqlite|mysql|postgresql|elasticsearch)')
  .option('--source-url <url>', 'Source connection string')
  .option('--target-url <url>', 'Target connection string')
  .option('--source-host <host>', 'Source host')
  .option('--source-port <port>', 'Source port', parseInt)
  .option('--source-database <database>', 'Source database name')
  .option('--source-username <username>', 'Source username')
  .option('--source-password <password>', 'Source password')
  .option('--target-host <host>', 'Target host')
  .option('--target-port <port>', 'Target port', parseInt)
  .option('--target-database <database>', 'Target database name')
  .option('--target-username <username>', 'Target username')
  .option('--target-password <password>', 'Target password')
  .option('--batch-size <size>', 'Batch size for migration', parseInt, 100)
  .option('--skip-existing', 'Skip existing records', false)
  .option('--dry-run', 'Perform a dry run without writing data', false)
  .option('--start-date <date>', 'Start date for migration (ISO format)')
  .option('--end-date <date>', 'End date for migration (ISO format)')
  .option('--types <types>', 'Comma-separated list of message types to migrate')
  .action(async (options) => {
    try {
      const sourceConfig: StorageConfig = {
        type: options.sourceType,
        connectionString: options.sourceUrl,
        host: options.sourceHost,
        port: options.sourcePort,
        database: options.sourceDatabase,
        username: options.sourceUsername,
        password: options.sourcePassword,
      };

      const targetConfig: StorageConfig = {
        type: options.targetType,
        connectionString: options.targetUrl,
        host: options.targetHost,
        port: options.targetPort,
        database: options.targetDatabase,
        username: options.targetUsername,
        password: options.targetPassword,
      };

      const migrationOptions: MigrationOptions = {
        batchSize: options.batchSize,
        skipExisting: options.skipExisting,
        dryRun: options.dryRun,
        startDate: options.startDate,
        endDate: options.endDate,
        types: options.types ? options.types.split(',').map((t: string) => t.trim()) : undefined,
      };

      const migrationTool = new DataMigrationTool(sourceConfig, targetConfig, migrationOptions);
      
      await migrationTool.initialize();
      const result = await migrationTool.migrate();
      
      console.log('\n📊 Migration Summary:');
      console.log(`Total records: ${result.total}`);
      console.log(`Processed: ${result.processed}`);
      console.log(`Errors: ${result.errors}`);
      console.log(`Duration: ${Date.now() - result.startTime.getTime()}ms`);
      
      if (!options.dryRun) {
        console.log('\n🔍 Validating migration...');
        const validation = await migrationTool.validateMigration();
        console.log(`Source count: ${validation.sourceCount}`);
        console.log(`Target count: ${validation.targetCount}`);
        console.log(`Match: ${validation.match ? '✅' : '❌'}`);
      }
      
      await migrationTool.cleanup();
      
      process.exit(result.errors > 0 ? 1 : 0);
    } catch (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
  });

program
  .command('validate')
  .description('Validate data consistency between two storage systems')
  .requiredOption('--source-type <type>', 'Source storage type')
  .requiredOption('--target-type <type>', 'Target storage type')
  .option('--source-url <url>', 'Source connection string')
  .option('--target-url <url>', 'Target connection string')
  .action(async (options) => {
    try {
      const sourceConfig: StorageConfig = {
        type: options.sourceType,
        connectionString: options.sourceUrl,
      };

      const targetConfig: StorageConfig = {
        type: options.targetType,
        connectionString: options.targetUrl,
      };

      const migrationTool = new DataMigrationTool(sourceConfig, targetConfig);
      await migrationTool.initialize();
      
      const validation = await migrationTool.validateMigration();
      
      console.log('📊 Validation Results:');
      console.log(`Source count: ${validation.sourceCount}`);
      console.log(`Target count: ${validation.targetCount}`);
      console.log(`Match: ${validation.match ? '✅' : '❌'}`);
      
      await migrationTool.cleanup();
      
      process.exit(validation.match ? 0 : 1);
    } catch (error) {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    }
  });

program
  .command('examples')
  .description('Show migration examples')
  .action(() => {
    console.log(`
📚 Migration Examples:

1. SQLite to MySQL:
   npm run migrate -- migrate \\
     --source-type sqlite \\
     --source-url "file:./dev.db" \\
     --target-type mysql \\
     --target-host localhost \\
     --target-port 3306 \\
     --target-database webhooks \\
     --target-username root \\
     --target-password password

2. MySQL to Elasticsearch:
   npm run migrate -- migrate \\
     --source-type mysql \\
     --source-host localhost \\
     --source-database webhooks \\
     --source-username root \\
     --source-password password \\
     --target-type elasticsearch \\
     --target-host localhost \\
     --target-port 9200

3. Dry run with date range:
   npm run migrate -- migrate \\
     --source-type sqlite \\
     --source-url "file:./dev.db" \\
     --target-type elasticsearch \\
     --target-url "http://localhost:9200" \\
     --dry-run \\
     --start-date "2024-01-01" \\
     --end-date "2024-12-31" \\
     --types "notification,alert"

4. Validate migration:
   npm run migrate -- validate \\
     --source-type sqlite \\
     --source-url "file:./dev.db" \\
     --target-type mysql \\
     --target-url "mysql://root:password@localhost:3306/webhooks"
`);
  });

if (require.main === module) {
  program.parse();
}

export { program };
