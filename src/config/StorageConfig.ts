import Joi from 'joi';
import { StorageConfig } from '../adapters/interfaces/IStorageAdapter';

export interface AppConfig {
  server: {
    port: number;
    host: string;
    cors: {
      origins: string[];
    };
  };
  storage: {
    primary: StorageConfig;
    fallback?: StorageConfig;
    enableFallback: boolean;
  };
  queue: {
    enabled: boolean;
    redis?: {
      host: string;
      port: number;
      password?: string;
      db: number;
    };
  };
  webhook: {
    secret?: string;
    batchSize: number;
    batchTimeout: number;
  };
  cleanup: {
    enabled: boolean;
    retentionDays: number;
    scheduleHour: number;
  };
}

const storageConfigSchema = Joi.object({
  type: Joi.string().valid('sqlite', 'mysql', 'postgresql', 'elasticsearch').required(),
  connectionString: Joi.string().optional(),
  host: Joi.string().optional(),
  port: Joi.number().optional(),
  database: Joi.string().optional(),
  username: Joi.string().optional(),
  password: Joi.string().optional(),
  ssl: Joi.boolean().optional(),
  maxConnections: Joi.number().min(1).max(100).optional(),
  timeout: Joi.number().min(1000).optional(),
  indexName: Joi.string().optional(), // For Elasticsearch
});

const configSchema = Joi.object({
  server: Joi.object({
    port: Joi.number().min(1).max(65535).default(3000),
    host: Joi.string().default('0.0.0.0'),
    cors: Joi.object({
      origins: Joi.array().items(Joi.string()).default(['http://localhost:3000']),
    }).default(),
  }).default(),
  
  storage: Joi.object({
    primary: storageConfigSchema.required(),
    fallback: storageConfigSchema.optional(),
    enableFallback: Joi.boolean().default(false),
  }).required(),
  
  queue: Joi.object({
    enabled: Joi.boolean().default(false),
    redis: Joi.object({
      host: Joi.string().default('localhost'),
      port: Joi.number().default(6379),
      password: Joi.string().optional(),
      db: Joi.number().default(0),
    }).optional(),
  }).default(),
  
  webhook: Joi.object({
    secret: Joi.string().optional(),
    batchSize: Joi.number().min(1).max(1000).default(100),
    batchTimeout: Joi.number().min(100).default(5000),
  }).default(),
  
  cleanup: Joi.object({
    enabled: Joi.boolean().default(true),
    retentionDays: Joi.number().min(1).default(30),
    scheduleHour: Joi.number().min(0).max(23).default(2),
  }).default(),
});

export class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadConfig(): AppConfig {
    const rawConfig = {
      server: {
        port: parseInt(process.env.PORT || '3000'),
        host: process.env.HOST || '0.0.0.0',
        cors: {
          origins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        },
      },
      storage: {
        primary: this.parseStorageConfig('PRIMARY'),
        fallback: this.parseStorageConfig('FALLBACK'),
        enableFallback: process.env.ENABLE_FALLBACK_STORAGE === 'true',
      },
      queue: {
        enabled: process.env.ENABLE_QUEUE === 'true',
        redis: process.env.REDIS_URL ? this.parseRedisUrl(process.env.REDIS_URL) : {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          db: parseInt(process.env.REDIS_DB || '0'),
        },
      },
      webhook: {
        secret: process.env.WEBHOOK_SECRET,
        batchSize: parseInt(process.env.WEBHOOK_BATCH_SIZE || '100'),
        batchTimeout: parseInt(process.env.WEBHOOK_BATCH_TIMEOUT || '5000'),
      },
      cleanup: {
        enabled: process.env.ENABLE_CLEANUP !== 'false',
        retentionDays: parseInt(process.env.RETENTION_DAYS || '30'),
        scheduleHour: parseInt(process.env.CLEANUP_HOUR || '2'),
      },
    };

    const { error, value } = configSchema.validate(rawConfig, { allowUnknown: true });
    
    if (error) {
      throw new Error(`Configuration validation error: ${error.message}`);
    }

    return value;
  }

  private parseStorageConfig(prefix: string): StorageConfig | undefined {
    const type = process.env[`${prefix}_STORAGE_TYPE`] as StorageConfig['type'];
    if (!type) {
      return prefix === 'PRIMARY' ? { type: 'sqlite', connectionString: 'file:./dev.db' } : undefined;
    }

    const config: StorageConfig = { type };

    // Common properties
    config.connectionString = process.env[`${prefix}_STORAGE_URL`];
    config.host = process.env[`${prefix}_STORAGE_HOST`];
    config.port = process.env[`${prefix}_STORAGE_PORT`] ? parseInt(process.env[`${prefix}_STORAGE_PORT`]) : undefined;
    config.database = process.env[`${prefix}_STORAGE_DATABASE`];
    config.username = process.env[`${prefix}_STORAGE_USERNAME`];
    config.password = process.env[`${prefix}_STORAGE_PASSWORD`];
    config.ssl = process.env[`${prefix}_STORAGE_SSL`] === 'true';
    config.maxConnections = process.env[`${prefix}_STORAGE_MAX_CONNECTIONS`] ? 
      parseInt(process.env[`${prefix}_STORAGE_MAX_CONNECTIONS`]) : undefined;
    config.timeout = process.env[`${prefix}_STORAGE_TIMEOUT`] ? 
      parseInt(process.env[`${prefix}_STORAGE_TIMEOUT`]) : undefined;

    // Elasticsearch specific
    if (type === 'elasticsearch') {
      config.indexName = process.env[`${prefix}_STORAGE_INDEX`] || 'webhook-messages';
    }

    return config;
  }

  private parseRedisUrl(url: string) {
    const parsed = new URL(url);
    return {
      host: parsed.hostname,
      port: parseInt(parsed.port) || 6379,
      password: parsed.password || undefined,
      db: parsed.pathname ? parseInt(parsed.pathname.slice(1)) : 0,
    };
  }

  public getConfig(): AppConfig {
    return this.config;
  }

  public getStorageConfig(): StorageConfig {
    return this.config.storage.primary;
  }

  public getFallbackStorageConfig(): StorageConfig | undefined {
    return this.config.storage.enableFallback ? this.config.storage.fallback : undefined;
  }

  public updateStorageConfig(config: StorageConfig): void {
    const { error } = storageConfigSchema.validate(config);
    if (error) {
      throw new Error(`Storage configuration validation error: ${error.message}`);
    }
    
    this.config.storage.primary = config;
  }

  public isQueueEnabled(): boolean {
    return this.config.queue.enabled;
  }

  public getWebhookConfig() {
    return this.config.webhook;
  }

  public getCleanupConfig() {
    return this.config.cleanup;
  }
}
