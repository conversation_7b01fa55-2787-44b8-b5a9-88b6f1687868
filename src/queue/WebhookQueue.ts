import Bull, { Queue, Job } from 'bull';
import Redis from 'ioredis';
import { IStorageAdapter } from '../adapters/interfaces/IStorageAdapter';
import { WebhookMessage } from '../types/webhook';
import { ConfigManager } from '../config/StorageConfig';

export interface WebhookJobData {
  messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[];
  batchId: string;
  timestamp: number;
}

export class WebhookQueue {
  private queue: Queue<WebhookJobData>;
  private redis: Redis;
  private storageAdapter: IStorageAdapter;
  private batchBuffer: Omit<WebhookMessage, 'id' | 'receivedAt'>[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private config = ConfigManager.getInstance().getWebhookConfig();

  constructor(storageAdapter: IStorageAdapter) {
    this.storageAdapter = storageAdapter;
    
    const redisConfig = ConfigManager.getInstance().getConfig().queue.redis!;
    
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      db: redisConfig.db,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    });

    this.queue = new Bull('webhook-processing', {
      redis: {
        host: redisConfig.host,
        port: redisConfig.port,
        password: redisConfig.password,
        db: redisConfig.db,
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.setupJobProcessor();
    this.setupEventHandlers();
  }

  private setupJobProcessor(): void {
    this.queue.process('batch-store', 5, async (job: Job<WebhookJobData>) => {
      const { messages, batchId } = job.data;
      
      console.log(`Processing batch ${batchId} with ${messages.length} messages`);
      
      try {
        const startTime = Date.now();
        const storedMessages = await this.storageAdapter.storeMessages(messages);
        const duration = Date.now() - startTime;
        
        console.log(`✅ Batch ${batchId} processed successfully in ${duration}ms`);
        
        // Update job progress
        await job.progress(100);
        
        return {
          batchId,
          processedCount: storedMessages.length,
          duration,
        };
      } catch (error) {
        console.error(`❌ Failed to process batch ${batchId}:`, error);
        throw error;
      }
    });
  }

  private setupEventHandlers(): void {
    this.queue.on('completed', (job, result) => {
      console.log(`Job ${job.id} completed:`, result);
    });

    this.queue.on('failed', (job, err) => {
      console.error(`Job ${job.id} failed:`, err.message);
    });

    this.queue.on('stalled', (job) => {
      console.warn(`Job ${job.id} stalled`);
    });
  }

  public async addMessage(message: Omit<WebhookMessage, 'id' | 'receivedAt'>): Promise<void> {
    this.batchBuffer.push(message);

    // If buffer is full, flush immediately
    if (this.batchBuffer.length >= this.config.batchSize) {
      await this.flushBatch();
    } else {
      // Set timer to flush batch after timeout
      this.resetBatchTimer();
    }
  }

  public async addMessages(messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[]): Promise<void> {
    // For large batches, process directly
    if (messages.length >= this.config.batchSize) {
      await this.processBatch(messages);
      return;
    }

    // Add to buffer
    this.batchBuffer.push(...messages);

    // If buffer is full, flush immediately
    if (this.batchBuffer.length >= this.config.batchSize) {
      await this.flushBatch();
    } else {
      this.resetBatchTimer();
    }
  }

  private resetBatchTimer(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = setTimeout(async () => {
      if (this.batchBuffer.length > 0) {
        await this.flushBatch();
      }
    }, this.config.batchTimeout);
  }

  private async flushBatch(): Promise<void> {
    if (this.batchBuffer.length === 0) return;

    const messages = [...this.batchBuffer];
    this.batchBuffer = [];

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    await this.processBatch(messages);
  }

  private async processBatch(messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[]): Promise<void> {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const jobData: WebhookJobData = {
      messages,
      batchId,
      timestamp: Date.now(),
    };

    await this.queue.add('batch-store', jobData, {
      priority: messages.length > 50 ? 10 : 5, // Higher priority for larger batches
    });

    console.log(`📦 Queued batch ${batchId} with ${messages.length} messages`);
  }

  public async getQueueStats() {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      this.queue.getWaiting(),
      this.queue.getActive(),
      this.queue.getCompleted(),
      this.queue.getFailed(),
      this.queue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
      bufferSize: this.batchBuffer.length,
    };
  }

  public async pauseQueue(): Promise<void> {
    await this.queue.pause();
    console.log('📴 Queue paused');
  }

  public async resumeQueue(): Promise<void> {
    await this.queue.resume();
    console.log('▶️ Queue resumed');
  }

  public async clearQueue(): Promise<void> {
    await this.queue.empty();
    console.log('🗑️ Queue cleared');
  }

  public async close(): Promise<void> {
    // Flush any remaining messages
    await this.flushBatch();

    // Wait for active jobs to complete
    await this.queue.close();
    
    // Close Redis connection
    await this.redis.quit();
    
    console.log('🔌 Webhook queue closed');
  }
}

export class BatchProcessor {
  private static instance: BatchProcessor;
  private queue?: WebhookQueue;
  private storageAdapter?: IStorageAdapter;

  private constructor() {}

  public static getInstance(): BatchProcessor {
    if (!BatchProcessor.instance) {
      BatchProcessor.instance = new BatchProcessor();
    }
    return BatchProcessor.instance;
  }

  public initialize(storageAdapter: IStorageAdapter): void {
    this.storageAdapter = storageAdapter;
    
    if (ConfigManager.getInstance().isQueueEnabled()) {
      this.queue = new WebhookQueue(storageAdapter);
      console.log('🚀 Batch processor initialized with queue');
    } else {
      console.log('🚀 Batch processor initialized without queue');
    }
  }

  public async processMessage(message: Omit<WebhookMessage, 'id' | 'receivedAt'>): Promise<WebhookMessage> {
    if (this.queue) {
      await this.queue.addMessage(message);
      // Return a placeholder - actual storage happens asynchronously
      return {
        id: 'queued',
        ...message,
        receivedAt: new Date(),
      } as WebhookMessage;
    } else {
      // Direct storage for immediate processing
      return await this.storageAdapter!.storeMessage(message);
    }
  }

  public async processMessages(messages: Omit<WebhookMessage, 'id' | 'receivedAt'>[]): Promise<WebhookMessage[]> {
    if (this.queue) {
      await this.queue.addMessages(messages);
      // Return placeholders - actual storage happens asynchronously
      return messages.map(message => ({
        id: 'queued',
        ...message,
        receivedAt: new Date(),
      })) as WebhookMessage[];
    } else {
      // Direct storage for immediate processing
      return await this.storageAdapter!.storeMessages(messages);
    }
  }

  public async getStats() {
    if (this.queue) {
      return await this.queue.getQueueStats();
    }
    return null;
  }

  public async close(): Promise<void> {
    if (this.queue) {
      await this.queue.close();
    }
  }
}
