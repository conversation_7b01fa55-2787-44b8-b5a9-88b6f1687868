import { IStorageAdapter, StorageConfig } from '../adapters/interfaces/IStorageAdapter';
import { StorageAdapterFactory } from '../adapters/StorageAdapterFactory';
import { WebhookMessage } from '../types/webhook';

export interface MigrationOptions {
  batchSize: number;
  skipExisting: boolean;
  dryRun: boolean;
  startDate?: string;
  endDate?: string;
  types?: string[];
}

export interface MigrationProgress {
  total: number;
  processed: number;
  errors: number;
  startTime: Date;
  estimatedTimeRemaining?: number;
}

export class DataMigrationTool {
  private sourceAdapter: IStorageAdapter;
  private targetAdapter: IStorageAdapter;
  private options: MigrationOptions;
  private progress: MigrationProgress;

  constructor(
    private sourceConfig: StorageConfig,
    private targetConfig: StorageConfig,
    options: Partial<MigrationOptions> = {}
  ) {
    this.options = {
      batchSize: 100,
      skipExisting: true,
      dryRun: false,
      ...options,
    };

    this.progress = {
      total: 0,
      processed: 0,
      errors: 0,
      startTime: new Date(),
    };
  }

  public async initialize(): Promise<void> {
    console.log('🔧 Initializing migration adapters...');
    
    this.sourceAdapter = await StorageAdapterFactory.createAdapter(this.sourceConfig);
    this.targetAdapter = await StorageAdapterFactory.createAdapter(this.targetConfig);
    
    console.log('✅ Migration adapters initialized');
  }

  public async migrate(): Promise<MigrationProgress> {
    console.log('🚀 Starting data migration...');
    console.log(`Source: ${this.sourceConfig.type}`);
    console.log(`Target: ${this.targetConfig.type}`);
    console.log(`Options:`, this.options);

    if (this.options.dryRun) {
      console.log('🧪 DRY RUN MODE - No data will be written');
    }

    try {
      // Get total count for progress tracking
      await this.calculateTotalRecords();
      
      // Perform migration in batches
      await this.migrateBatches();
      
      console.log('✅ Migration completed successfully');
      console.log(`📊 Final stats: ${this.progress.processed}/${this.progress.total} processed, ${this.progress.errors} errors`);
      
      return this.progress;
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  private async calculateTotalRecords(): Promise<void> {
    console.log('📊 Calculating total records...');
    
    const queryParams: any = {
      page: 1,
      pageSize: 1,
    };

    if (this.options.startDate) {
      queryParams.startDate = this.options.startDate;
    }
    
    if (this.options.endDate) {
      queryParams.endDate = this.options.endDate;
    }

    if (this.options.types && this.options.types.length > 0) {
      // For multiple types, we'll need to sum them up
      let total = 0;
      for (const type of this.options.types) {
        const result = await this.sourceAdapter.getMessages({ ...queryParams, type });
        total += result.total;
      }
      this.progress.total = total;
    } else {
      const result = await this.sourceAdapter.getMessages(queryParams);
      this.progress.total = result.total;
    }

    console.log(`📈 Total records to migrate: ${this.progress.total}`);
  }

  private async migrateBatches(): Promise<void> {
    const { batchSize } = this.options;
    let currentPage = 1;
    let hasMore = true;

    while (hasMore) {
      console.log(`📦 Processing batch ${currentPage} (${this.progress.processed}/${this.progress.total})`);
      
      const queryParams: any = {
        page: currentPage,
        pageSize: batchSize,
      };

      if (this.options.startDate) {
        queryParams.startDate = this.options.startDate;
      }
      
      if (this.options.endDate) {
        queryParams.endDate = this.options.endDate;
      }

      let allMessages: WebhookMessage[] = [];

      if (this.options.types && this.options.types.length > 0) {
        // Process each type separately
        for (const type of this.options.types) {
          const result = await this.sourceAdapter.getMessages({ ...queryParams, type });
          allMessages.push(...result.messages);
        }
      } else {
        const result = await this.sourceAdapter.getMessages(queryParams);
        allMessages = result.messages;
      }

      if (allMessages.length === 0) {
        hasMore = false;
        break;
      }

      // Process the batch
      await this.processBatch(allMessages);
      
      // Update progress
      this.progress.processed += allMessages.length;
      this.updateEstimatedTime();
      
      // Check if we have more data
      hasMore = allMessages.length === batchSize;
      currentPage++;

      // Small delay to prevent overwhelming the systems
      await this.sleep(100);
    }
  }

  private async processBatch(messages: WebhookMessage[]): Promise<void> {
    const messagesToMigrate: Omit<WebhookMessage, 'id' | 'receivedAt'>[] = [];

    for (const message of messages) {
      try {
        // Check if message already exists in target (if skipExisting is enabled)
        if (this.options.skipExisting) {
          const existing = await this.targetAdapter.getMessageById(message.id);
          if (existing) {
            console.log(`⏭️ Skipping existing message: ${message.id}`);
            continue;
          }
        }

        // Prepare message for migration (remove id and receivedAt to let target generate new ones)
        const { id, receivedAt, ...messageData } = message;
        messagesToMigrate.push(messageData);

      } catch (error) {
        console.error(`❌ Error processing message ${message.id}:`, error);
        this.progress.errors++;
      }
    }

    if (messagesToMigrate.length > 0 && !this.options.dryRun) {
      try {
        await this.targetAdapter.storeMessages(messagesToMigrate);
        console.log(`✅ Migrated ${messagesToMigrate.length} messages`);
      } catch (error) {
        console.error(`❌ Error storing batch:`, error);
        this.progress.errors += messagesToMigrate.length;
      }
    } else if (this.options.dryRun) {
      console.log(`🧪 DRY RUN: Would migrate ${messagesToMigrate.length} messages`);
    }
  }

  private updateEstimatedTime(): void {
    const elapsed = Date.now() - this.progress.startTime.getTime();
    const rate = this.progress.processed / elapsed; // messages per ms
    const remaining = this.progress.total - this.progress.processed;
    
    if (rate > 0) {
      this.progress.estimatedTimeRemaining = remaining / rate;
    }

    const percentage = ((this.progress.processed / this.progress.total) * 100).toFixed(1);
    const eta = this.progress.estimatedTimeRemaining 
      ? new Date(Date.now() + this.progress.estimatedTimeRemaining).toLocaleTimeString()
      : 'Unknown';
    
    console.log(`📈 Progress: ${percentage}% (${this.progress.processed}/${this.progress.total}) - ETA: ${eta}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public async validateMigration(): Promise<{
    sourceCount: number;
    targetCount: number;
    match: boolean;
  }> {
    console.log('🔍 Validating migration...');
    
    const sourceStats = await this.sourceAdapter.getStats();
    const targetStats = await this.targetAdapter.getStats();
    
    const result = {
      sourceCount: sourceStats.total,
      targetCount: targetStats.total,
      match: sourceStats.total === targetStats.total,
    };

    if (result.match) {
      console.log('✅ Migration validation passed');
    } else {
      console.log(`⚠️ Migration validation failed: source=${result.sourceCount}, target=${result.targetCount}`);
    }

    return result;
  }

  public async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up migration resources...');
    
    if (this.sourceAdapter) {
      await StorageAdapterFactory.closeAdapter(this.sourceConfig);
    }
    
    if (this.targetAdapter) {
      await StorageAdapterFactory.closeAdapter(this.targetConfig);
    }
    
    console.log('✅ Migration cleanup completed');
  }

  public getProgress(): MigrationProgress {
    return { ...this.progress };
  }
}
